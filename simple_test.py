#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from three_sniper_strategy import ThreeStateSniper

def test_symbol_filtering():
    """测试币种过滤功能"""
    print("🎯 测试币种过滤功能")
    print("=" * 40)
    
    # 测试配置
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {
                'smart_proxy': True,
                'auto_detect': True,
                'target_server_ip': '*************',
                'enabled': False
            }
        }
    }
    
    try:
        print("1. 创建策略实例...")
        strategy = ThreeStateSniper(config)
        
        print("2. 获取涨幅最高币种...")
        top_symbols = strategy.get_top_symbols_by_change()
        
        if top_symbols:
            print(f"✅ 成功获取 {len(top_symbols)} 个币种:")
            for i, symbol in enumerate(top_symbols[:5], 1):
                print(f"  {i}. {symbol}")
        else:
            print("❌ 未能获取币种列表")
            return
        
        print("3. 测试K线数据获取...")
        test_symbol = top_symbols[0]
        print(f"测试币种: {test_symbol}")
        
        df = strategy.fetch_klines_to_df(test_symbol)
        if df is not None:
            print(f"✅ 成功获取K线数据: {len(df)} 根K线")
            print(f"  最新价格: {df['close'].iloc[-1]:.4f}")
        else:
            print("❌ 获取K线数据失败")
        
        print("4. 测试已下架币种...")
        invalid_symbols = ['ALPACAUSDT', 'AWEUSDT']
        for symbol in invalid_symbols:
            print(f"测试 {symbol}...")
            df = strategy.fetch_klines_to_df(symbol)
            if df is not None:
                print(f"  意外成功获取数据")
            else:
                print(f"  ✅ 正确处理了无效币种")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_symbol_filtering()
