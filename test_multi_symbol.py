#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多币种功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from three_sniper_strategy import ThreeStateSniper

# 配置示例
config = {
    'api': {
        'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
        'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
    },
    'network': {
        'proxy': {
            'enabled': True,
            'type': 'http',
            'host': '127.0.0.1',
            'port': 7897
        }
    }
}

def test_get_top_symbols():
    """测试获取涨幅最高币种功能"""
    print("=== 测试获取涨幅最高币种功能 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    # 获取涨幅最高的前10个币种
    top_symbols = strategy.get_top_symbols_by_change()
    
    print("获取到的币种列表:")
    for i, symbol in enumerate(top_symbols, 1):
        print(f"{i}. {symbol}")
    
    print(f"\n总共获取到 {len(top_symbols)} 个币种")
    
    # 验证结果
    if len(top_symbols) > 0:
        print("✅ 功能测试通过")
        return True
    else:
        print("❌ 功能测试失败")
        return False

if __name__ == "__main__":
    test_get_top_symbols()