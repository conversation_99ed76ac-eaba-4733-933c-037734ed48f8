#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三态狙击手策略完整演示脚本
展示所有三个任务功能的使用方法
"""

import sys
import os
import time
import threading
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from three_sniper_strategy import ThreeStateSniper

# 配置示例
config = {
    'api': {
        'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
        'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
    },
    'network': {
        'proxy': {
            'enabled': True,
            'type': 'http',
            'host': '127.0.0.1',
            'port': 7897
        }
    }
}

def demonstrate_task1():
    """演示任务1：保本止损功能"""
    print("=== 演示任务1：保本止损功能 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    # 模拟仓位信息
    strategy.has_open_position = True
    strategy.position_info = {
        'symbol': 'AVNTUSDT',
        'entry_price': 1.0,
        'stop_loss': 0.95,
        'size': 100
    }
    strategy.open_orders = {}
    
    print(f"开仓信息:")
    print(f"  交易对: {strategy.position_info['symbol']}")
    print(f"  开仓价: {strategy.position_info['entry_price']}")
    print(f"  止损价: {strategy.position_info['stop_loss']}")
    print(f"  仓位大小: {strategy.position_info['size']}")
    
    # 模拟当前价格上涨超过2%
    current_price = 1.021  # 超过2%的涨幅
    
    print(f"\n当前价格: {current_price}")
    
    # 检查是否触发保本止损条件
    entry_price = strategy.position_info['entry_price']
    price_increase = (current_price - entry_price) / entry_price
    print(f"价格涨幅: {price_increase*100:.2f}%")
    
    if price_increase >= strategy.trailing_stop_threshold:
        print(f"\n✅ 触发保本止损条件!")
        print(f"  触发阈值: {strategy.trailing_stop_threshold*100}%")
        print(f"  保本位置: {strategy.trailing_stop_offset*100}%")
        
        # 演示更新保本止损
        print(f"\n正在更新保本止损...")
        # 这里我们只演示逻辑，不实际调用API
        new_stop_loss = entry_price * (1 + strategy.trailing_stop_offset)
        print(f"  新的止损价: {new_stop_loss:.4f}")
        print(f"  原止损价: {strategy.position_info['stop_loss']}")
        print(f"  ✅ 保本止损已更新!")

def demonstrate_task2():
    """演示任务2：多币种支持"""
    print("\n=== 演示任务2：多币种支持 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    print(f"配置参数:")
    print(f"  追踪币种数量: {strategy.top_symbols_count}")
    print(f"  最大仓位数: {strategy.max_positions}")
    print(f"  仓位资金比例: {strategy.position_size_percent*100}%")
    
    # 获取涨幅最高的前10个币种
    print(f"\n正在获取24小时涨幅最高的币种...")
    top_symbols = strategy.get_top_symbols_by_change()
    
    print(f"\n当前追踪的前{len(top_symbols)}个币种:")
    for i, symbol in enumerate(top_symbols, 1):
        print(f"  {i:2d}. {symbol}")
    
    print(f"\n✅ 多币种支持演示完成!")

def demonstrate_task3():
    """演示任务3：仓位管理"""
    print("\n=== 演示任务3：仓位管理 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    print(f"仓位管理配置:")
    print(f"  最大允许仓位数: {strategy.max_positions}")
    print(f"  每个仓位资金比例: {strategy.position_size_percent*100}%")
    
    # 模拟账户余额
    account_balance = 10000.0  # 10000 USDT
    
    # 模拟开仓参数
    entry_price = 1.0
    stop_loss = 0.95
    
    # 计算仓位大小
    position_size = strategy.calculate_position_size(account_balance, entry_price, stop_loss)
    
    print(f"\n账户信息:")
    print(f"  账户余额: {account_balance} USDT")
    print(f"  入场价: {entry_price}")
    print(f"  止损价: {stop_loss}")
    print(f"  计算出的仓位大小: {position_size:.6f}")
    
    # 验证风险控制
    risk_per_unit = abs(entry_price - stop_loss)
    actual_risk = position_size * risk_per_unit
    expected_risk = account_balance * strategy.position_size_percent
    
    print(f"\n风险控制:")
    print(f"  每单位风险: {risk_per_unit}")
    print(f"  实际风险金额: {actual_risk:.2f} USDT")
    print(f"  预期风险金额: {expected_risk:.2f} USDT")
    print(f"  风险控制误差: {abs(actual_risk - expected_risk):.6f} USDT")
    
    print(f"\n✅ 仓位管理演示完成!")

def demonstrate_multi_symbol_strategy():
    """演示多币种策略运行"""
    print("\n=== 演示多币种策略运行 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    print("多币种策略运行说明:")
    print("1. 策略会自动获取24小时涨幅最高的前10个币种")
    print("2. 最多同时持有3个币种的仓位")
    print("3. 每个仓位使用账户资金的1%进行交易")
    print("4. 当价格上涨超过开仓价2%时，自动更新保本止损")
    
    # 获取初始币种列表
    print(f"\n正在获取初始币种列表...")
    top_symbols = strategy.get_top_symbols_by_change()
    
    if top_symbols:
        print(f"获取到 {len(top_symbols)} 个币种:")
        for i, symbol in enumerate(top_symbols[:5], 1):  # 只显示前5个
            print(f"  {i}. {symbol}")
        if len(top_symbols) > 5:
            print(f"  ... 还有 {len(top_symbols) - 5} 个币种")
        
        print(f"\n策略将按顺序检查这些币种的交易机会...")
        print(f"当前活跃仓位数: {len(strategy.active_positions)}")
        print(f"最大允许仓位数: {strategy.max_positions}")
        
        print(f"\n✅ 多币种策略演示完成!")

def main():
    """主演示函数"""
    print("🎯 三态狙击手策略完整功能演示")
    print("=" * 50)
    
    # 演示任务1
    demonstrate_task1()
    
    # 演示任务2
    demonstrate_task2()
    
    # 演示任务3
    demonstrate_task3()
    
    # 演示多币种策略
    demonstrate_multi_symbol_strategy()
    
    print("\n" + "=" * 50)
    print("🎉 所有功能演示完成!")
    print("\n使用说明:")
    print("1. 运行策略: python three_sniper_strategy.py")
    print("2. 选择运行模式:")
    print("   - 单币种模式: 专注交易一个币种")
    print("   - 多币种模式: 自动追踪涨幅最高的币种")
    print("3. 策略会自动处理:")
    print("   - 趋势识别和入场点选择")
    print("   - 动态仓位管理和风险控制")
    print("   - 保本止损和止盈设置")
    print("   - 多币种仓位限制")

if __name__ == "__main__":
    main()