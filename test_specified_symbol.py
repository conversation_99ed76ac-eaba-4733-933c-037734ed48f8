#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指定币种模式测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from three_sniper_strategy import ThreeStateSniper

# 配置示例
config = {
    'api': {
        'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
        'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
    },
    'network': {
        'proxy': {
            'enabled': True,
            'type': 'http',
            'host': '127.0.0.1',
            'port': 7897
        }
    }
}

def test_symbol_validation():
    """测试币种验证功能"""
    print("=== 测试币种验证功能 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    # 测试有效的币种
    valid_symbols = ['BTCUSDT', 'btcusdt', 'ETHUSDT', 'ethusdt', 'AVNTUSDT']
    print("测试有效的币种:")
    for symbol in valid_symbols:
        try:
            # 转换为大写以确保兼容性
            normalized_symbol = symbol.upper()
            print(f"  {symbol} -> {normalized_symbol}")
        except Exception as e:
            print(f"  {symbol} -> 错误: {e}")
    
    print("\n✅ 币种验证功能测试完成")

def test_symbol_formatting():
    """测试币种格式化功能"""
    print("\n=== 测试币种格式化功能 ===")
    
    test_cases = [
        'btcusdt',
        'BTCUSDT', 
        'ethusdt',
        'ETHUSDT',
        'AvntUsdt',
        'bNBusdt'
    ]
    
    print("币种格式化测试:")
    for symbol in test_cases:
        formatted = symbol.upper()
        print(f"  {symbol} -> {formatted}")
    
    print("\n✅ 币种格式化功能测试完成")

if __name__ == "__main__":
    test_symbol_validation()
    test_symbol_formatting()