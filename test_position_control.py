#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 仓位控制测试脚本
验证多币种模式下的仓位风险控制和止盈止损逻辑
"""

import sys
import time
from three_sniper_strategy import ThreeStateSniper

def test_position_calculation():
    """测试仓位计算逻辑"""
    print("🧪 测试仓位计算逻辑...")
    
    # 模拟配置
    config = {
        'api': {
            'api_key': 'test_key',
            'api_secret': 'test_secret'
        },
        'network': {
            'proxy': {
                'smart_proxy': False,
                'enabled': False
            }
        }
    }
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    # 模拟账户余额
    account_balance = 10000  # $10,000
    
    # 测试场景1：第一个仓位
    print("\n📊 测试场景1：第一个仓位")
    entry_price = 50000
    stop_loss = 49000
    
    size1 = strategy.calculate_position_size(account_balance, entry_price, stop_loss)
    print(f"  入场价: ${entry_price}")
    print(f"  止损价: ${stop_loss}")
    print(f"  风险距离: ${entry_price - stop_loss}")
    print(f"  计算仓位: {size1:.6f}")
    print(f"  仓位价值: ${size1 * entry_price:.2f}")
    print(f"  最大亏损: ${size1 * (entry_price - stop_loss):.2f}")
    print(f"  风险百分比: {(size1 * (entry_price - stop_loss)) / account_balance * 100:.2f}%")
    
    # 模拟添加第一个仓位
    strategy.active_positions['BTCUSDT'] = {
        'entry_price': entry_price,
        'size': size1,
        'stop_loss': stop_loss
    }
    
    # 测试场景2：第二个仓位
    print("\n📊 测试场景2：第二个仓位")
    entry_price2 = 3000
    stop_loss2 = 2900
    
    size2 = strategy.calculate_position_size(account_balance, entry_price2, stop_loss2)
    print(f"  入场价: ${entry_price2}")
    print(f"  止损价: ${stop_loss2}")
    print(f"  风险距离: ${entry_price2 - stop_loss2}")
    print(f"  计算仓位: {size2:.6f}")
    print(f"  仓位价值: ${size2 * entry_price2:.2f}")
    print(f"  最大亏损: ${size2 * (entry_price2 - stop_loss2):.2f}")
    print(f"  风险百分比: {(size2 * (entry_price2 - stop_loss2)) / account_balance * 100:.2f}%")
    
    # 模拟添加第二个仓位
    strategy.active_positions['ETHUSDT'] = {
        'entry_price': entry_price2,
        'size': size2,
        'stop_loss': stop_loss2
    }
    
    # 测试场景3：第三个仓位
    print("\n📊 测试场景3：第三个仓位")
    entry_price3 = 100
    stop_loss3 = 95
    
    size3 = strategy.calculate_position_size(account_balance, entry_price3, stop_loss3)
    print(f"  入场价: ${entry_price3}")
    print(f"  止损价: ${stop_loss3}")
    print(f"  风险距离: ${entry_price3 - stop_loss3}")
    print(f"  计算仓位: {size3:.6f}")
    print(f"  仓位价值: ${size3 * entry_price3:.2f}")
    print(f"  最大亏损: ${size3 * (entry_price3 - stop_loss3):.2f}")
    print(f"  风险百分比: {(size3 * (entry_price3 - stop_loss3)) / account_balance * 100:.2f}%")
    
    # 计算总风险
    total_risk = 0
    total_position_value = 0
    
    print("\n📈 总风险分析:")
    for symbol, position in strategy.active_positions.items():
        risk = position['size'] * (position['entry_price'] - position['stop_loss'])
        value = position['size'] * position['entry_price']
        total_risk += risk
        total_position_value += value
        print(f"  {symbol}: 风险 ${risk:.2f} ({risk/account_balance*100:.2f}%), 价值 ${value:.2f}")
    
    print(f"\n🎯 总计:")
    print(f"  总风险: ${total_risk:.2f} ({total_risk/account_balance*100:.2f}%)")
    print(f"  总仓位价值: ${total_position_value:.2f} ({total_position_value/account_balance*100:.2f}%)")
    print(f"  当前仓位数: {len(strategy.active_positions)}/{strategy.max_positions}")

def test_position_limits():
    """测试仓位限制逻辑"""
    print("\n🧪 测试仓位限制逻辑...")
    
    config = {
        'api': {
            'api_key': 'test_key',
            'api_secret': 'test_secret'
        },
        'network': {
            'proxy': {
                'smart_proxy': False,
                'enabled': False
            }
        }
    }
    
    strategy = ThreeStateSniper(config)
    account_balance = 10000
    
    # 测试场景：尝试开启超过最大仓位数的仓位
    print(f"\n📊 最大仓位数测试 (限制: {strategy.max_positions})")
    
    symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
    
    for i, symbol in enumerate(symbols):
        print(f"\n  尝试开启第{i+1}个仓位: {symbol}")
        
        # 模拟信号信息
        signal_info = {
            'signal': 'BUY',
            'entry_price': 1000 + i * 100,
            'stop_loss': 950 + i * 100,
            'take_profit': 1150 + i * 100
        }
        
        # 检查是否会被仓位限制阻止
        if len(strategy.active_positions) >= strategy.max_positions:
            print(f"    ❌ 被仓位限制阻止 (当前: {len(strategy.active_positions)}/{strategy.max_positions})")
            continue
            
        if symbol in strategy.active_positions:
            print(f"    ❌ 币种已有仓位")
            continue
            
        # 计算仓位
        size = strategy.calculate_position_size(account_balance, signal_info['entry_price'], signal_info['stop_loss'])
        
        if size <= 0:
            print(f"    ❌ 仓位计算结果为0")
            continue
            
        # 模拟添加仓位
        strategy.active_positions[symbol] = {
            'symbol': symbol,
            'size': size,
            'entry_price': signal_info['entry_price'],
            'stop_loss': signal_info['stop_loss'],
            'take_profit': signal_info['take_profit'],
            'entry_time': time.time(),
            'open_orders': {}
        }
        
        print(f"    ✅ 仓位已开启 (当前: {len(strategy.active_positions)}/{strategy.max_positions})")
        print(f"       仓位大小: {size:.6f}")
        print(f"       仓位价值: ${size * signal_info['entry_price']:.2f}")

def main():
    """主测试函数"""
    print("🎯 三态狙击手策略 - 仓位控制测试")
    print("=" * 50)
    
    try:
        # 测试仓位计算
        test_position_calculation()
        
        # 测试仓位限制
        test_position_limits()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
