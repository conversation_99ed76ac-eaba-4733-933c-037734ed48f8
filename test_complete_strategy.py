#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整策略功能测试脚本
测试所有三个任务的功能：
1. 保本止损功能
2. 多币种支持
3. 仓位管理
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from three_sniper_strategy import ThreeStateSniper

# 配置示例
config = {
    'api': {
        'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
        'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
    },
    'network': {
        'proxy': {
            'enabled': True,
            'type': 'http',
            'host': '127.0.0.1',
            'port': 7897
        }
    }
}

def test_task1_trailing_stop():
    """测试任务1：保本止损功能"""
    print("=== 测试任务1：保本止损功能 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    # 模拟仓位信息
    strategy.has_open_position = True
    strategy.position_info = {
        'symbol': 'AVNTUSDT',
        'entry_price': 1.0,
        'stop_loss': 0.95,
        'size': 100
    }
    
    # 模拟当前价格上涨超过2%
    current_price = 1.021  # 超过2%的涨幅
    
    print(f"开仓价: {strategy.position_info['entry_price']}")
    print(f"当前止损价: {strategy.position_info['stop_loss']}")
    print(f"当前价格: {current_price}")
    
    # 检查是否触发保本止损条件
    entry_price = strategy.position_info['entry_price']
    price_increase = (current_price - entry_price) / entry_price
    print(f"价格涨幅: {price_increase*100:.2f}%")
    
    if price_increase >= strategy.trailing_stop_threshold:
        print("✅ 触发保本止损条件")
        # 计算新的保本止损价格
        new_stop_loss = entry_price * (1 + strategy.trailing_stop_offset)
        print(f"新的保本止损价: {new_stop_loss:.4f}")
        print("✅ 保本止损功能测试通过")
        return True
    else:
        print("❌ 未触发保本止损条件")
        return False

def test_task2_multi_symbol():
    """测试任务2：多币种支持"""
    print("\n=== 测试任务2：多币种支持 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    # 获取涨幅最高的前10个币种
    top_symbols = strategy.get_top_symbols_by_change()
    
    print("获取到的币种列表:")
    for i, symbol in enumerate(top_symbols, 1):
        print(f"{i}. {symbol}")
    
    print(f"\n总共获取到 {len(top_symbols)} 个币种")
    
    # 验证是否获取到正确的币种数量
    if len(top_symbols) > 0 and len(top_symbols) <= strategy.top_symbols_count:
        print("✅ 多币种支持功能测试通过")
        return True
    else:
        print("❌ 多币种支持功能测试失败")
        return False

def test_task3_position_management():
    """测试任务3：仓位管理"""
    print("\n=== 测试任务3：仓位管理 ===")
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)
    
    print(f"最大允许仓位数: {strategy.max_positions}")
    print(f"每个仓位资金比例: {strategy.position_size_percent*100}%")
    
    # 模拟账户余额
    account_balance = 10000.0  # 10000 USDT
    
    # 模拟开仓参数
    entry_price = 1.0
    stop_loss = 0.95
    
    # 计算仓位大小
    position_size = strategy.calculate_position_size(account_balance, entry_price, stop_loss)
    
    print(f"账户余额: {account_balance} USDT")
    print(f"入场价: {entry_price}")
    print(f"止损价: {stop_loss}")
    print(f"计算出的仓位大小: {position_size}")
    
    # 验证仓位大小是否正确（账户的1%）
    expected_risk = account_balance * strategy.position_size_percent
    actual_risk = position_size * abs(entry_price - stop_loss)
    
    print(f"预期风险金额: {expected_risk} USDT")
    print(f"实际风险金额: {actual_risk} USDT")
    
    # 检查是否在合理范围内
    if abs(expected_risk - actual_risk) < 0.01:  # 允许小数精度误差
        print("✅ 仓位管理功能测试通过")
        return True
    else:
        print("❌ 仓位管理功能测试失败")
        return False

def test_all_tasks():
    """测试所有任务"""
    print("开始测试三态狙击手策略的所有功能...\n")
    
    # 测试任务1
    task1_result = test_task1_trailing_stop()
    
    # 测试任务2
    task2_result = test_task2_multi_symbol()
    
    # 测试任务3
    task3_result = test_task3_position_management()
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"任务1 (保本止损): {'✅ 通过' if task1_result else '❌ 失败'}")
    print(f"任务2 (多币种支持): {'✅ 通过' if task2_result else '❌ 失败'}")
    print(f"任务3 (仓位管理): {'✅ 通过' if task3_result else '❌ 失败'}")
    
    if task1_result and task2_result and task3_result:
        print("\n🎉 所有功能测试通过！")
        return True
    else:
        print("\n❌ 部分功能测试失败！")
        return False

if __name__ == "__main__":
    test_all_tasks()