#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果
"""

import requests
import json

def test_binance_api():
    """直接测试币安API"""
    print("测试币安API...")
    
    try:
        # 测试获取交易所信息
        url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取交易所信息")
            
            # 统计活跃的USDT永续合约
            active_count = 0
            trading_count = 0
            
            for symbol_info in data.get('symbols', []):
                if (symbol_info.get('contractType') == 'PERPETUAL' and
                    symbol_info.get('symbol', '').endswith('USDT')):
                    active_count += 1
                    if symbol_info.get('status') == 'TRADING':
                        trading_count += 1
            
            print(f"总USDT永续合约数: {active_count}")
            print(f"正在交易的合约数: {trading_count}")
            
            # 检查ALPACAUSDT是否在列表中
            alpaca_found = False
            awe_found = False
            
            for symbol_info in data.get('symbols', []):
                if symbol_info.get('symbol') == 'ALPACAUSDT':
                    alpaca_found = True
                    print(f"ALPACAUSDT状态: {symbol_info.get('status')}")
                elif symbol_info.get('symbol') == 'AWEUSDT':
                    awe_found = True
                    print(f"AWEUSDT状态: {symbol_info.get('status')}")
            
            if not alpaca_found:
                print("✅ ALPACAUSDT不在交易所列表中（已下架）")
            if not awe_found:
                print("✅ AWEUSDT不在交易所列表中（已下架）")
            
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_24hr_ticker():
    """测试24小时行情数据"""
    print("\n测试24小时行情数据...")
    
    try:
        url = 'https://fapi.binance.com/fapi/v1/ticker/24hr'
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取24小时行情数据，共{len(data)}个币种")
            
            # 检查ALPACAUSDT和AWEUSDT
            alpaca_found = False
            awe_found = False
            
            for ticker in data:
                if ticker.get('symbol') == 'ALPACAUSDT':
                    alpaca_found = True
                    print(f"ALPACAUSDT在24小时行情中")
                elif ticker.get('symbol') == 'AWEUSDT':
                    awe_found = True
                    print(f"AWEUSDT在24小时行情中")
            
            if not alpaca_found:
                print("✅ ALPACAUSDT不在24小时行情中")
            if not awe_found:
                print("✅ AWEUSDT不在24小时行情中")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🔍 验证币种过滤修复效果")
    print("=" * 40)
    
    test_binance_api()
    test_24hr_ticker()
    
    print("\n✅ 验证完成！")
