#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试策略输出
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from three_sniper_strategy import ThreeStateSniper

def test_strategy_output():
    """测试策略输出是否正常"""
    print("🎯 测试策略输出")
    print("=" * 40)
    
    # 测试配置
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {
                'smart_proxy': True,
                'auto_detect': True,
                'target_server_ip': '*************',
                'enabled': False
            }
        }
    }
    
    try:
        print("1. 创建策略实例...")
        strategy = ThreeStateSniper(config)
        
        print("2. 获取币种列表...")
        top_symbols = strategy.get_top_symbols_by_change()
        
        if not top_symbols:
            print("❌ 未能获取币种列表")
            return
        
        print(f"✅ 获取到 {len(top_symbols)} 个币种")
        
        print("3. 测试单个币种策略运行...")
        test_symbol = top_symbols[0]
        print(f"测试币种: {test_symbol}")
        
        # 运行一次策略
        strategy.run_strategy(test_symbol)
        
        print("4. 测试优化版策略运行...")
        strategy.run_strategy_optimized(test_symbol)
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategy_output()
