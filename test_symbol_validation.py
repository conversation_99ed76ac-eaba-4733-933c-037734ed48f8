#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币种验证功能测试脚本
测试活跃永续合约获取和币种验证功能
"""

import sys
import time
import logging
from three_sniper_strategy import ThreeStateSniper

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_symbol_validation():
    """测试币种验证功能"""
    print("🔍 币种验证功能测试")
    print("=" * 50)
    
    # 测试配置
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {
                'smart_proxy': True,
                'auto_detect': True,
                'target_server_ip': '*************',
                'enabled': False,
                'type': 'http',
                'host': '127.0.0.1',
                'port': 7897
            }
        }
    }
    
    try:
        # 创建策略实例
        print("\n1️⃣ 创建策略实例...")
        strategy = ThreeStateSniper(config)
        
        # 测试获取涨幅最高币种
        print("\n2️⃣ 获取涨幅最高的活跃币种...")
        top_symbols = strategy.get_top_symbols_by_change()

        if top_symbols:
            print(f"✅ 获取到 {len(top_symbols)} 个涨幅最高的币种:")
            for i, symbol in enumerate(top_symbols, 1):
                print(f"  {i:2d}. {symbol}")
        else:
            print("❌ 未能获取涨幅最高币种")

        # 测试K线数据获取
        print("\n3️⃣ 测试K线数据获取...")
        if top_symbols:
            test_symbol = top_symbols[0]
            print(f"测试币种: {test_symbol}")

            df = strategy.fetch_klines_to_df(test_symbol)
            if df is not None:
                print(f"✅ 成功获取K线数据: {len(df)} 根K线")
                print(f"  最新价格: {df['close'].iloc[-1]:.4f}")
                print(f"  24小时成交量: {df['volume'].sum():.2f}")
            else:
                print("❌ 获取K线数据失败")

        # 测试一些可能已下架的币种
        print("\n4️⃣ 测试已下架币种处理...")
        test_symbols = ['ALPACAUSDT', 'AWEUSDT', 'INVALIDUSDT']

        for symbol in test_symbols:
            print(f"测试币种: {symbol}")
            df = strategy.fetch_klines_to_df(symbol)
            if df is not None:
                print(f"  ✅ 成功获取K线数据")
            else:
                print(f"  ❌ 无法获取K线数据（预期结果）")
        
        print("\n✅ 币种验证测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_exchange_info():
    """测试交易所信息获取"""
    print("\n📊 交易所信息测试")
    print("=" * 30)

    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {'proxy': {'enabled': False}}
    }

    try:
        strategy = ThreeStateSniper(config)

        # 直接测试交易所信息API
        exchange_info = strategy.trader.http.get('/fapi/v1/exchangeInfo')

        if exchange_info and 'symbols' in exchange_info:
            print(f"✅ 成功获取交易所信息")

            # 统计活跃的USDT永续合约
            active_count = 0
            trading_count = 0

            for symbol_info in exchange_info['symbols']:
                if (symbol_info.get('contractType') == 'PERPETUAL' and
                    symbol_info.get('symbol', '').endswith('USDT')):
                    active_count += 1
                    if symbol_info.get('status') == 'TRADING':
                        trading_count += 1

            print(f"  总USDT永续合约数: {active_count}")
            print(f"  正在交易的合约数: {trading_count}")

            # 显示一些示例
            print("\n前10个正在交易的USDT永续合约:")
            count = 0
            for symbol_info in exchange_info['symbols']:
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('contractType') == 'PERPETUAL' and
                    symbol_info.get('symbol', '').endswith('USDT')):
                    print(f"  {count+1:2d}. {symbol_info['symbol']}")
                    count += 1
                    if count >= 10:
                        break
        else:
            print("❌ 获取交易所信息失败")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🎯 币种验证系统测试")
    print("=" * 60)
    
    # 主要功能测试
    test_symbol_validation()

    # 交易所信息测试
    test_exchange_info()
    
    print("\n🎉 所有测试完成！")
