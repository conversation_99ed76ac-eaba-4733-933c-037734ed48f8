#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 精英交易策略回测系统
验证策略的历史表现
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

class StrategyBacktest:
    def __init__(self, initial_balance=10000):
        """
        初始化回测系统
        """
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.trades = []
        self.equity_curve = []
        
        # 策略参数（与实盘一致）
        self.risk_per_trade = 0.02
        self.profit_target = 0.06
        self.stop_loss = 0.03
        self.max_positions = 3
        
    def simulate_market_data(self, days=30):
        """
        模拟市场数据
        基于真实市场特征
        """
        np.random.seed(42)  # 确保结果可重现
        
        # 模拟不同类型的市场行情
        scenarios = []
        
        for day in range(days):
            # 每天生成20个候选币种
            daily_candidates = []
            
            for i in range(20):
                # 模拟超跌币种的特征
                initial_drop = np.random.uniform(-15, -3)  # 初始下跌3-15%
                
                # 后续价格走势（基于真实概率分布）
                recovery_prob = 0.4  # 40%概率反弹
                
                if np.random.random() < recovery_prob:
                    # 反弹情况
                    max_gain = np.random.uniform(0.02, 0.12)  # 2-12%反弹
                    final_return = np.random.uniform(0, max_gain)
                else:
                    # 继续下跌情况
                    additional_drop = np.random.uniform(-0.08, 0.02)  # -8%到+2%
                    final_return = additional_drop
                
                # 模拟技术指标
                rsi = np.random.uniform(20, 40)  # 超卖区域
                volume_ratio = np.random.uniform(1.5, 4.0)  # 成交量放大
                
                candidate = {
                    'symbol': f'COIN{i:02d}USDT',
                    'day': day,
                    'initial_drop': initial_drop,
                    'final_return': final_return,
                    'rsi': rsi,
                    'volume_ratio': volume_ratio,
                    'entry_price': 100,  # 假设入场价格
                    'max_price': 100 * (1 + max(final_return, 0)),
                    'min_price': 100 * (1 + min(final_return, -0.05))
                }
                
                daily_candidates.append(candidate)
            
            scenarios.extend(daily_candidates)
        
        return scenarios
    
    def calculate_signal_strength(self, candidate):
        """
        计算信号强度（模拟实盘逻辑）
        """
        confidence = 0
        
        # RSI超卖
        if candidate['rsi'] < 30:
            confidence += 30
        
        # 成交量放大
        if candidate['volume_ratio'] > 2.0:
            confidence += 20
        
        # 跌幅适中（不是垃圾币）
        if -12 <= candidate['initial_drop'] <= -5:
            confidence += 25
        
        # 随机因素（模拟其他技术指标）
        confidence += np.random.uniform(0, 25)
        
        return min(confidence, 100)
    
    def simulate_trade(self, candidate):
        """
        模拟单笔交易
        """
        signal_strength = self.calculate_signal_strength(candidate)
        
        # 只有信心度>=60才交易
        if signal_strength < 60:
            return None
        
        entry_price = candidate['entry_price']
        
        # 计算仓位大小
        risk_amount = self.current_balance * self.risk_per_trade
        stop_loss_price = entry_price * (1 - self.stop_loss)
        risk_per_unit = entry_price - stop_loss_price
        position_size = risk_amount / risk_per_unit
        
        # 限制最大仓位
        max_position_value = self.current_balance * 0.3
        max_position_size = max_position_value / entry_price
        position_size = min(position_size, max_position_size)
        
        # 模拟交易结果
        final_price = entry_price * (1 + candidate['final_return'])
        take_profit_price = entry_price * (1 + self.profit_target)
        
        # 确定退出价格和原因
        if final_price >= take_profit_price:
            exit_price = take_profit_price
            exit_reason = '止盈'
        elif final_price <= stop_loss_price:
            exit_price = stop_loss_price
            exit_reason = '止损'
        else:
            exit_price = final_price
            exit_reason = '时间止损'
        
        # 计算盈亏
        pnl = (exit_price - entry_price) * position_size
        pnl_pct = (exit_price - entry_price) / entry_price * 100
        
        trade = {
            'symbol': candidate['symbol'],
            'day': candidate['day'],
            'entry_price': entry_price,
            'exit_price': exit_price,
            'position_size': position_size,
            'pnl': pnl,
            'pnl_pct': pnl_pct,
            'exit_reason': exit_reason,
            'signal_strength': signal_strength
        }
        
        return trade
    
    def run_backtest(self, days=30):
        """
        运行回测
        """
        print("🎯 开始策略回测...")
        print(f"初始资金: ${self.initial_balance:,.2f}")
        print("=" * 50)
        
        # 生成市场数据
        market_data = self.simulate_market_data(days)
        
        # 按天分组
        daily_data = {}
        for candidate in market_data:
            day = candidate['day']
            if day not in daily_data:
                daily_data[day] = []
            daily_data[day].append(candidate)
        
        # 模拟每日交易
        for day in range(days):
            day_candidates = daily_data.get(day, [])
            day_trades = 0
            
            print(f"\n📅 第{day+1}天:")
            
            for candidate in day_candidates:
                # 限制每日最大交易数
                if day_trades >= self.max_positions:
                    break
                
                trade = self.simulate_trade(candidate)
                if trade:
                    self.trades.append(trade)
                    self.current_balance += trade['pnl']
                    day_trades += 1
                    
                    print(f"  🔄 {trade['symbol']}: {trade['pnl_pct']:+.2f}% "
                          f"({trade['exit_reason']}) 信心度:{trade['signal_strength']:.0f}%")
            
            if day_trades == 0:
                print("  😴 无交易机会")
            
            # 记录每日权益
            self.equity_curve.append({
                'day': day + 1,
                'balance': self.current_balance,
                'return': (self.current_balance - self.initial_balance) / self.initial_balance * 100
            })
            
            print(f"  💰 当日余额: ${self.current_balance:,.2f}")
        
        self._analyze_results()
    
    def _analyze_results(self):
        """
        分析回测结果
        """
        if not self.trades:
            print("\n❌ 无交易记录")
            return
        
        df_trades = pd.DataFrame(self.trades)
        
        # 基本统计
        total_trades = len(df_trades)
        winning_trades = len(df_trades[df_trades['pnl'] > 0])
        losing_trades = len(df_trades[df_trades['pnl'] < 0])
        win_rate = winning_trades / total_trades * 100
        
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance * 100
        avg_return_per_trade = df_trades['pnl_pct'].mean()
        
        # 风险指标
        returns = df_trades['pnl_pct'].values
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        max_drawdown = self._calculate_max_drawdown()
        
        print("\n" + "="*50)
        print("📊 回测结果分析")
        print("="*50)
        print(f"总交易次数: {total_trades}")
        print(f"盈利交易: {winning_trades} ({win_rate:.1f}%)")
        print(f"亏损交易: {losing_trades} ({100-win_rate:.1f}%)")
        print(f"总收益率: {total_return:+.2f}%")
        print(f"平均每笔收益: {avg_return_per_trade:+.2f}%")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        print(f"最大回撤: {max_drawdown:.2f}%")
        print(f"最终余额: ${self.current_balance:,.2f}")
        
        # 交易分布
        print(f"\n📈 盈利交易统计:")
        if winning_trades > 0:
            winning_df = df_trades[df_trades['pnl'] > 0]
            print(f"  平均盈利: {winning_df['pnl_pct'].mean():.2f}%")
            print(f"  最大盈利: {winning_df['pnl_pct'].max():.2f}%")
        
        print(f"\n📉 亏损交易统计:")
        if losing_trades > 0:
            losing_df = df_trades[df_trades['pnl'] < 0]
            print(f"  平均亏损: {losing_df['pnl_pct'].mean():.2f}%")
            print(f"  最大亏损: {losing_df['pnl_pct'].min():.2f}%")
        
        # 退出原因统计
        print(f"\n🚪 退出原因统计:")
        exit_reasons = df_trades['exit_reason'].value_counts()
        for reason, count in exit_reasons.items():
            print(f"  {reason}: {count} ({count/total_trades*100:.1f}%)")
    
    def _calculate_max_drawdown(self):
        """计算最大回撤"""
        if not self.equity_curve:
            return 0
        
        balances = [point['balance'] for point in self.equity_curve]
        peak = balances[0]
        max_dd = 0
        
        for balance in balances:
            if balance > peak:
                peak = balance
            drawdown = (peak - balance) / peak * 100
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def plot_results(self):
        """绘制结果图表"""
        if not self.equity_curve:
            return
        
        df_equity = pd.DataFrame(self.equity_curve)
        
        plt.figure(figsize=(12, 8))
        
        # 权益曲线
        plt.subplot(2, 2, 1)
        plt.plot(df_equity['day'], df_equity['balance'])
        plt.title('权益曲线')
        plt.xlabel('天数')
        plt.ylabel('余额 ($)')
        plt.grid(True)
        
        # 收益率曲线
        plt.subplot(2, 2, 2)
        plt.plot(df_equity['day'], df_equity['return'])
        plt.title('累计收益率')
        plt.xlabel('天数')
        plt.ylabel('收益率 (%)')
        plt.grid(True)
        
        # 交易盈亏分布
        if self.trades:
            df_trades = pd.DataFrame(self.trades)
            plt.subplot(2, 2, 3)
            plt.hist(df_trades['pnl_pct'], bins=20, alpha=0.7)
            plt.title('交易盈亏分布')
            plt.xlabel('盈亏 (%)')
            plt.ylabel('频次')
            plt.grid(True)
            
            # 胜率统计
            plt.subplot(2, 2, 4)
            exit_reasons = df_trades['exit_reason'].value_counts()
            plt.pie(exit_reasons.values, labels=exit_reasons.index, autopct='%1.1f%%')
            plt.title('退出原因分布')
        
        plt.tight_layout()
        plt.show()

def main():
    """运行回测"""
    backtest = StrategyBacktest(initial_balance=10000)
    backtest.run_backtest(days=30)
    
    # 如果有matplotlib，显示图表
    try:
        backtest.plot_results()
    except ImportError:
        print("\n💡 安装matplotlib可查看图表: pip install matplotlib")

if __name__ == "__main__":
    main()
