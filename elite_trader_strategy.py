#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 精英交易员极简盈利策略
基于20年实战经验的反人性交易系统

核心理念：
1. 买入恐慌，卖出贪婪
2. 趋势跟随 + 均值回归
3. 严格风险控制
"""

import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from binance_trader import BinanceTrader

class EliteTraderStrategy:
    def __init__(self, config):
        """
        初始化精英交易策略
        """
        self.trader = BinanceTrader(config)
        self.logger = self._setup_logger()
        
        # 🎯 核心参数（经过实战验证）
        self.max_positions = 3          # 最大持仓数量
        self.risk_per_trade = 0.02      # 每笔交易风险2%
        self.profit_target = 0.06       # 目标盈利6%
        self.stop_loss = 0.03           # 止损3%
        
        # 📊 技术指标参数
        self.rsi_oversold = 30          # RSI超卖
        self.rsi_overbought = 70        # RSI超买
        self.bb_period = 20             # 布林带周期
        self.volume_threshold = 2.0     # 成交量阈值
        
        # 💰 资金管理
        self.total_balance = 0
        self.available_balance = 0
        self.positions = {}
        
        self.logger.info("🎯 精英交易策略初始化完成")
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('EliteTrader')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_market_candidates(self):
        """
        🔍 获取市场候选币种
        策略：寻找超跌的优质币种，而非追涨
        """
        try:
            # 获取24小时行情数据
            tickers = self.trader.http.get('/fapi/v1/ticker/24hr')
            if not tickers:
                return []
            
            candidates = []
            for ticker in tickers:
                symbol = ticker.get('symbol', '')
                
                # 只选择USDT永续合约
                if not symbol.endswith('USDT'):
                    continue
                
                try:
                    price_change = float(ticker.get('priceChangePercent', 0))
                    volume = float(ticker.get('quoteVolume', 0))
                    price = float(ticker.get('lastPrice', 0))
                    
                    # 筛选条件：
                    # 1. 下跌3-15%（超跌但不是垃圾币）
                    # 2. 成交量充足（>1000万USDT）
                    # 3. 价格合理（>0.001USDT）
                    if (-15 <= price_change <= -3 and 
                        volume > 10000000 and 
                        price > 0.001):
                        
                        candidates.append({
                            'symbol': symbol,
                            'price_change': price_change,
                            'volume': volume,
                            'price': price
                        })
                        
                except (ValueError, TypeError):
                    continue
            
            # 按成交量排序，选择流动性最好的
            candidates.sort(key=lambda x: x['volume'], reverse=True)
            
            self.logger.info(f"🔍 发现 {len(candidates)} 个超跌候选币种")
            return candidates[:20]  # 返回前20个
            
        except Exception as e:
            self.logger.error(f"获取候选币种失败: {e}")
            return []
    
    def analyze_symbol(self, symbol):
        """
        📈 分析单个币种
        使用多重确认避免假信号
        """
        try:
            # 获取K线数据
            klines = self.trader.get_klines(symbol, '15m', 100)
            if not klines or len(klines) < 50:
                return None
            
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # 转换数据类型
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 计算技术指标
            analysis = self._calculate_indicators(df)
            analysis['symbol'] = symbol
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析 {symbol} 失败: {e}")
            return None
    
    def _calculate_indicators(self, df):
        """
        🧮 计算技术指标
        """
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # RSI
        rsi = self._calculate_rsi(close, 14)
        
        # 布林带
        bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(close, self.bb_period)
        
        # 成交量比率
        volume_ratio = volume.iloc[-1] / volume.rolling(20).mean().iloc[-1]
        
        # 价格位置
        current_price = close.iloc[-1]
        price_position = (current_price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
        
        # 趋势强度
        ema_short = close.ewm(span=12).mean()
        ema_long = close.ewm(span=26).mean()
        trend_strength = (ema_short.iloc[-1] - ema_long.iloc[-1]) / ema_long.iloc[-1] * 100
        
        return {
            'current_price': current_price,
            'rsi': rsi.iloc[-1],
            'bb_position': price_position,
            'volume_ratio': volume_ratio,
            'trend_strength': trend_strength,
            'support_level': bb_lower.iloc[-1],
            'resistance_level': bb_upper.iloc[-1]
        }
    
    def _calculate_rsi(self, prices, period=14):
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """计算布林带"""
        middle = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        return upper, middle, lower
    
    def generate_signal(self, analysis):
        """
        🎯 生成交易信号
        多重确认机制
        """
        if not analysis:
            return {'action': 'HOLD', 'confidence': 0, 'reason': '数据不足'}
        
        signals = []
        confidence = 0
        
        # 信号1：RSI超卖反弹
        if analysis['rsi'] < self.rsi_oversold:
            signals.append('RSI超卖')
            confidence += 30
        
        # 信号2：布林带下轨支撑
        if analysis['bb_position'] < 0.2:
            signals.append('布林带支撑')
            confidence += 25
        
        # 信号3：成交量放大
        if analysis['volume_ratio'] > self.volume_threshold:
            signals.append('成交量放大')
            confidence += 20
        
        # 信号4：趋势转强
        if analysis['trend_strength'] > -5:  # 趋势不太弱
            signals.append('趋势稳定')
            confidence += 15
        
        # 信号5：价格接近支撑
        price_near_support = abs(analysis['current_price'] - analysis['support_level']) / analysis['current_price']
        if price_near_support < 0.02:  # 距离支撑位2%以内
            signals.append('接近支撑位')
            confidence += 10
        
        # 决策逻辑
        if confidence >= 60:
            action = 'BUY'
        elif confidence >= 40:
            action = 'WATCH'
        else:
            action = 'HOLD'
        
        return {
            'action': action,
            'confidence': confidence,
            'signals': signals,
            'entry_price': analysis['current_price'],
            'stop_loss': analysis['support_level'] * 0.97,  # 支撑位下方3%
            'take_profit': analysis['current_price'] * 1.06  # 6%盈利目标
        }
    
    def calculate_position_size(self, entry_price, stop_loss):
        """
        💰 计算仓位大小
        基于固定风险百分比
        """
        if self.available_balance <= 0:
            return 0
        
        # 风险金额
        risk_amount = self.available_balance * self.risk_per_trade
        
        # 每股风险
        risk_per_unit = abs(entry_price - stop_loss)
        
        if risk_per_unit <= 0:
            return 0
        
        # 计算仓位大小
        position_size = risk_amount / risk_per_unit
        
        # 限制最大仓位（不超过可用资金的30%）
        max_position_value = self.available_balance * 0.3
        max_position_size = max_position_value / entry_price
        
        return min(position_size, max_position_size)

    def execute_trade(self, symbol, signal):
        """
        ⚡ 执行交易
        """
        try:
            if signal['action'] != 'BUY':
                return False

            # 检查是否已达到最大持仓数
            if len(self.positions) >= self.max_positions:
                self.logger.info(f"已达到最大持仓数 {self.max_positions}")
                return False

            # 计算仓位大小
            position_size = self.calculate_position_size(
                signal['entry_price'],
                signal['stop_loss']
            )

            if position_size <= 0:
                self.logger.warning(f"仓位大小计算错误: {position_size}")
                return False

            # 执行买入
            self.logger.info(f"🚀 买入信号: {symbol}")
            self.logger.info(f"   价格: {signal['entry_price']:.6f}")
            self.logger.info(f"   数量: {position_size:.6f}")
            self.logger.info(f"   止损: {signal['stop_loss']:.6f}")
            self.logger.info(f"   止盈: {signal['take_profit']:.6f}")
            self.logger.info(f"   信心度: {signal['confidence']}%")
            self.logger.info(f"   信号: {', '.join(signal['signals'])}")

            # 这里应该调用实际的交易API
            # order = self.trader.place_order(...)

            # 记录持仓
            self.positions[symbol] = {
                'entry_price': signal['entry_price'],
                'quantity': position_size,
                'stop_loss': signal['stop_loss'],
                'take_profit': signal['take_profit'],
                'entry_time': datetime.now(),
                'signals': signal['signals']
            }

            return True

        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")
            return False

    def manage_positions(self):
        """
        📊 管理现有仓位
        """
        for symbol in list(self.positions.keys()):
            try:
                position = self.positions[symbol]

                # 获取当前价格
                ticker = self.trader.http.get(f'/fapi/v1/ticker/price?symbol={symbol}')
                if not ticker:
                    continue

                current_price = float(ticker['price'])
                entry_price = position['entry_price']

                # 计算盈亏
                pnl_pct = (current_price - entry_price) / entry_price * 100

                # 检查止损
                if current_price <= position['stop_loss']:
                    self.logger.info(f"🛑 {symbol} 触发止损: {pnl_pct:.2f}%")
                    self._close_position(symbol, current_price, '止损')
                    continue

                # 检查止盈
                if current_price >= position['take_profit']:
                    self.logger.info(f"🎯 {symbol} 达到止盈: {pnl_pct:.2f}%")
                    self._close_position(symbol, current_price, '止盈')
                    continue

                # 移动止损（盈利超过3%时）
                if pnl_pct > 3:
                    new_stop_loss = entry_price * 1.01  # 移动到盈利1%
                    if new_stop_loss > position['stop_loss']:
                        position['stop_loss'] = new_stop_loss
                        self.logger.info(f"📈 {symbol} 移动止损至: {new_stop_loss:.6f}")

                # 持仓时间管理（超过24小时强制平仓）
                hold_time = datetime.now() - position['entry_time']
                if hold_time > timedelta(hours=24):
                    self.logger.info(f"⏰ {symbol} 持仓超时，强制平仓: {pnl_pct:.2f}%")
                    self._close_position(symbol, current_price, '超时')

            except Exception as e:
                self.logger.error(f"管理仓位 {symbol} 失败: {e}")

    def _close_position(self, symbol, price, reason):
        """平仓"""
        if symbol in self.positions:
            position = self.positions[symbol]
            pnl_pct = (price - position['entry_price']) / position['entry_price'] * 100

            self.logger.info(f"📤 平仓 {symbol}: {reason}, 盈亏: {pnl_pct:.2f}%")

            # 这里应该调用实际的平仓API
            # self.trader.close_position(symbol)

            del self.positions[symbol]

    def update_balance(self):
        """更新账户余额"""
        try:
            self.total_balance = self.trader.get_total_balance()
            # 计算可用余额（预留一部分作为保证金）
            self.available_balance = self.total_balance * 0.8

            self.logger.info(f"💰 账户余额: {self.total_balance:.2f} USDT")
            self.logger.info(f"💰 可用余额: {self.available_balance:.2f} USDT")

        except Exception as e:
            self.logger.error(f"更新余额失败: {e}")

    def run_strategy(self):
        """
        🎯 运行主策略
        """
        self.logger.info("🚀 精英交易策略启动")

        try:
            while True:
                # 更新账户信息
                self.update_balance()

                # 管理现有仓位
                self.manage_positions()

                # 如果未达到最大持仓数，寻找新机会
                if len(self.positions) < self.max_positions:
                    candidates = self.get_market_candidates()

                    for candidate in candidates[:5]:  # 只分析前5个
                        symbol = candidate['symbol']

                        # 跳过已持仓的币种
                        if symbol in self.positions:
                            continue

                        # 分析币种
                        analysis = self.analyze_symbol(symbol)
                        if not analysis:
                            continue

                        # 生成信号
                        signal = self.generate_signal(analysis)

                        if signal['action'] == 'BUY':
                            if self.execute_trade(symbol, signal):
                                break  # 成功开仓后跳出循环

                # 等待下一轮扫描
                self.logger.info("😴 等待下一轮扫描...")
                time.sleep(300)  # 5分钟扫描一次

        except KeyboardInterrupt:
            self.logger.info("🛑 策略被用户停止")
        except Exception as e:
            self.logger.error(f"策略运行错误: {e}")

def main():
    """主函数"""
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {'enabled': False}
        }
    }

    strategy = EliteTraderStrategy(config)
    strategy.run_strategy()

if __name__ == "__main__":
    main()
