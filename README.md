# 三态狙击手策略 (Three-State Sniper Strategy)

一个基于趋势识别和动态风险管理的量化交易策略，专为币安期货市场设计。

## 🎯 核心功能

### 1. 保本止损功能
- 当价格上涨超过开仓价2%时，自动将保本止损价移动到开仓价+0.3%的位置
- 防止利润回吐，保护已实现收益

### 2. 多币种支持
- 自动追踪币安市场24小时涨幅最高的前10个币种
- 动态调整关注币种列表，捕捉市场热点

### 3. 仓位管理
- 最多允许开3个币种仓位
- 每个币种仓位大小为总账户的1%动态仓位
- 严格的风险控制和资金管理

## 🚀 快速开始

### 安装依赖
```bash
pip install pandas numpy requests
```

### 配置API密钥
在代码中配置您的币安API密钥：
```python
config = {
    'api': {
        'api_key': 'your_api_key',
        'api_secret': 'your_api_secret'
    },
    'network': {
        'proxy': {
            'enabled': True,
            'type': 'http',
            'host': '127.0.0.1',
            'port': 7897
        }
    }
}
```

### 运行策略
```bash
python three_sniper_strategy.py
```

选择运行模式：
1. 单币种模式 - 专注交易一个币种
2. 多币种模式 - 自动追踪涨幅最高的币种
3. 指定币种模式 - 用户指定交易币种（新增）

## 📊 策略特点

### 趋势识别
- 使用价格与均线关系判断市场方向
- 结合ATR波动率指标确认趋势强度
- 只在明确趋势中寻找交易机会

### 入场机制
- 等待价格回调到关键支撑位时入场
- 提高入场赔率，优化风险回报比
- 固定1:3的风险回报比设置

### 风险管理
- 动态仓位计算基于账户资金的1%
- 逐仓模式和10倍杠杆设置
- 自动止损和止盈订单

### 多币种优化
- 实时监控市场热点币种
- 分散投资降低单一币种风险
- 仓位限制防止过度交易

## 🛠 技术实现

### 核心参数
```python
# 趋势识别参数
atr_period = 14
trend_strength_threshold = 2.0

# 入场参数
entry_buffer_pct = 0.3

# 风险管理参数
stop_loss_atr_multiple = 1.0
take_profit_atr_multiple = 3.0
max_risk_per_trade = 0.01

# 保本止损参数
trailing_stop_threshold = 0.02  # 2%
trailing_stop_offset = 0.003    # 0.3%

# 多币种参数
top_symbols_count = 10
max_positions = 3
position_size_percent = 0.01
```

## 📈 策略优势

1. **严格的风控体系** - 每笔交易风险控制在账户的1%以内
2. **动态仓位管理** - 根据市场波动率调整仓位大小
3. **智能止损机制** - 保本止损保护已实现利润
4. **多币种分散** - 降低单一币种风险，提高收益稳定性
5. **灵活的币种支持** - 支持单币种、多币种和指定币种模式
6. **不区分大小写** - 币种输入支持大小写混写，自动转换为标准格式

## 📝 使用说明

### 单币种模式
适合专注交易特定币种的用户，策略会持续监控该币种的交易机会。

### 多币种模式
适合希望捕捉市场热点的用户，策略会自动：
1. 获取24小时涨幅最高的前10个币种
2. 按顺序检查每个币种的交易机会
3. 最多同时持有3个币种仓位
4. 每个仓位使用账户资金的1%

### 指定币种模式（新增）
适合希望交易特定币种的用户：
1. 用户可以输入任意格式的币种名称（如 btcusdt, BTCUSDT, BtcUsdt 等）
2. 系统会自动转换为标准大写格式
3. 策略会持续监控指定币种的交易机会

## ⚠️ 风险提示

1. 加密货币交易存在高风险，可能导致本金损失
2. 策略表现可能因市场条件变化而不同
3. 建议在实盘交易前进行充分的回测和模拟交易
4. 请根据自身风险承受能力调整仓位大小

## 📞 技术支持

如有问题，请联系开发团队或提交GitHub issue。