import pandas as pd
import numpy as np
import time
import logging
import warnings
from binance_trader import BinanceTrader
from proxy_manager import ProxyManager

# 禁用urllib3的InsecureRequestWarning警告
from urllib3.exceptions import InsecureRequestWarning
warnings.simplefilter('ignore', InsecureRequestWarning)

class ThreeStateSniper:
    """
    顶尖交易员的极简生存策略：三态狙击手
    只在市场处于明确趋势时，以高赔率入场。
    """

    def __init__(self, config):
        self.config = config
        # 核心参数：完全动态，基于ATR
        self.atr_period = 14
        self.trend_strength_threshold = 2.0 # ATR倍数，用于判断趋势强度
        self.entry_buffer_pct = 0.3 # 在趋势确认后，等待回调X%再入场，提高赔率
        self.stop_loss_atr_multiple = 1.0 # 止损 = 1倍ATR
        self.take_profit_atr_multiple = 3.0 # 止盈 = 3倍ATR (1:3 风险回报比)
        self.max_risk_per_trade = 0.01 # 单笔交易最大风险：账户的1%

        # 保本止损参数
        self.trailing_stop_threshold = 0.02 # 2%触发保本止损
        self.trailing_stop_offset = 0.003   # 0.3%保本位置

        # 多币种支持参数
        self.top_symbols_count = 10  # 追踪前10个币种
        self.max_positions = 3       # 最多允许3个仓位
        self.position_size_percent = 0.01  # 每个仓位占账户的1%

        # 🎯 分层频率优化配置
        self.frequency_config = {
            # 第一层：超高频风险监控（生命线）
            'risk_monitor': 2,          # 2秒 - 止损保护检查
            'emergency_check': 1,       # 1秒 - 急跌保护
            'trailing_stop_update': 3,  # 3秒 - 保本止损更新

            # 第二层：高频信号检测（机会捕捉）
            'price_monitor': 30,        # 30秒 - 价格突破监控
            'atr_update': 60,          # 60秒 - ATR波动率更新
            'trend_check': 45,         # 45秒 - 趋势状态确认

            # 第三层：中频策略执行（核心逻辑）
            'signal_generation': 120,   # 2分钟 - 完整信号生成
            'position_management': 180, # 3分钟 - 仓位管理检查
            'technical_analysis': 300,  # 5分钟 - 技术指标计算

            # 第四层：低频市场扫描（全局视野）
            'symbol_ranking': 600,      # 10分钟 - 币种排名更新
            'market_overview': 900,     # 15分钟 - 市场状态评估
            'account_check': 1800,      # 30分钟 - 账户余额检查
        }

        # 动态频率调整参数
        self.market_volatility_multiplier = 1.0  # 市场波动率调整系数
        self.position_risk_multiplier = 1.0      # 仓位风险调整系数
        self.last_execution_times = {}           # 记录各模块最后执行时间

        # 初始化交易器和代理管理器
        self.trader = BinanceTrader(config)
        self.proxy_manager = ProxyManager(config.get('network', {}))

        # 设置日志
        self.logger = logging.getLogger('ThreeStateSniper')
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

        # 🚀 智能代理状态监控
        self.proxy_optimization_interval = 1800  # 30分钟检查一次代理优化
        self.last_proxy_optimization = 0


        
        # 添加仓位管理属性
        self.has_open_position = False
        self.position_info = {}
        self.open_orders = {}

        # 多币种支持属性
        self.top_symbols = []  # 当前追踪的前N个币种
        self.active_positions = {}  # 当前活跃仓位 {symbol: position_info}

        # 缓存机制
        self.price_cache = {}           # 价格数据缓存
        self.indicator_cache = {}       # 技术指标缓存
        self.market_data_cache = {}     # 市场数据缓存
        self.cache_expiry = {}          # 缓存过期时间

    def should_execute_module(self, module_name):
        """
        🎯 智能频率控制：判断是否应该执行特定模块
        根据分层频率配置和动态调整因子决定执行时机
        """
        current_time = time.time()

        # 获取基础频率
        base_interval = self.frequency_config.get(module_name, 300)

        # 动态调整频率
        adjusted_interval = self.calculate_dynamic_interval(module_name, base_interval)

        # 检查是否到达执行时间
        last_execution = self.last_execution_times.get(module_name, 0)
        if current_time - last_execution >= adjusted_interval:
            self.last_execution_times[module_name] = current_time
            return True
        return False

    def calculate_dynamic_interval(self, module_name, base_interval):
        """
        📊 动态频率调整算法
        根据市场状态和仓位风险动态调整执行频率
        """
        # 基础间隔
        interval = base_interval

        # 市场波动率调整
        if hasattr(self, 'current_volatility'):
            if self.current_volatility > 1.5:  # 高波动
                interval *= 0.7  # 提高频率30%
            elif self.current_volatility < 0.5:  # 低波动
                interval *= 1.3  # 降低频率30%

        # 仓位风险调整
        if self.has_open_position:
            # 有仓位时，风险监控模块频率提高
            if module_name in ['risk_monitor', 'trailing_stop_update', 'emergency_check']:
                interval *= 0.5  # 频率提高100%
            # 信号生成频率可以降低
            elif module_name in ['signal_generation', 'symbol_ranking']:
                interval *= 1.5  # 频率降低50%

        # 确保最小间隔限制
        min_intervals = {
            'emergency_check': 0.5,     # 最快0.5秒
            'risk_monitor': 1,          # 最快1秒
            'trailing_stop_update': 2,  # 最快2秒
        }

        if module_name in min_intervals:
            interval = max(interval, min_intervals[module_name])

        return interval

    def calculate_atr(self, df, period):
        """计算ATR，衡量市场波动率"""
        df['tr'] = np.maximum(df['high'] - df['low'],
                             np.maximum(abs(df['high'] - df['close'].shift(1)),
                                       abs(df['low'] - df['close'].shift(1))))
        df['atr'] = df['tr'].rolling(period).mean()

        # 更新当前波动率用于动态调整
        if len(df) > 0:
            latest_atr = df['atr'].iloc[-1]
            latest_price = df['close'].iloc[-1]
            if not pd.isna(latest_atr) and latest_price > 0:
                self.current_volatility = latest_atr / latest_price

        return df

    def identify_market_state(self, df):
        """
        识别市场状态：这是策略的灵魂
        使用：价格与均线的关系 + ATR波动率
        """
        # 计算中长期均线 (判断方向)
        df['ma_slow'] = df['close'].rolling(window=50).mean()
        
        # 计算ATR (判断波动和趋势强度)
        df = self.calculate_atr(df, self.atr_period)
        
        # 判断趋势状态
        # 条件1: 价格在慢均线之上 (多头方向)
        # 条件2: ATR > 慢均线的 X% (市场有足够波动，非死水)
        df['is_uptrend'] = (df['close'] > df['ma_slow']) & (df['atr'] > (df['ma_slow'] * 0.01 * self.trend_strength_threshold))
        
        # 判断震荡状态
        df['is_chop'] = ~df['is_uptrend'] & (df['atr'] < (df['ma_slow'] * 0.01 * self.trend_strength_threshold))
        
        return df

    def generate_signal(self, df):
        """
        生成交易信号
        只在趋势状态中，等待价格回调到关键支撑位（提高赔率）时入场
        """
        df = self.identify_market_state(df)
        latest = df.iloc[-1]
        prev = df.iloc[-2]

        signal = None
        entry_price = None
        stop_loss = None
        take_profit = None

        # 核心逻辑：只做多，且只在上升趋势中
        if latest['is_uptrend']:
            # 等待价格从高点回调，提高入场赔率
            recent_high = df['high'].iloc[-5:].max() # 过去5根K线的最高点
            pullback_entry = recent_high * (1 - self.entry_buffer_pct / 100.0)
            
            if latest['close'] <= pullback_entry and prev['close'] > pullback_entry:
                # 价格回调到目标位，产生买入信号
                signal = 'BUY'
                entry_price = latest['close']
                stop_loss = entry_price - (latest['atr'] * self.stop_loss_atr_multiple)
                take_profit = entry_price + (latest['atr'] * self.take_profit_atr_multiple)
                
                print("[SNIPER] 信号: %s @ %.4f | SL: %.4f | TP: %.4f | 风报比: 1:%.1f" % (signal, entry_price, stop_loss, take_profit, self.take_profit_atr_multiple/self.stop_loss_atr_multiple))
                self.logger.info("[SNIPER] 信号: %s @ %.4f | SL: %.4f | TP: %.4f | 风报比: 1:%.1f" % (signal, entry_price, stop_loss, take_profit, self.take_profit_atr_multiple/self.stop_loss_atr_multiple))

        # 离场逻辑：无条件
        # 无论当前是盈利还是亏损，只要市场状态不再是"上升趋势"，立即离场。
        elif not latest['is_uptrend'] and self.has_open_position:
            signal = 'SELL'
            entry_price = latest['close'] # 市价离场
            print("[SNIPER] 离场: 市场状态改变")
            self.logger.info("[SNIPER] 离场: 市场状态改变")

        return {
            'signal': signal,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'current_state': 'Uptrend' if latest['is_uptrend'] else 'Chop'
        }

    def calculate_position_size(self, account_balance, entry_price, stop_loss):
        """
        基于波动率和账户风险计算仓位
        这是顶尖交易员和普通交易员的核心区别
        """
        # 使用账户的1%作为风险金额
        risk_amount = account_balance * self.position_size_percent
        risk_per_unit = abs(entry_price - stop_loss) # 每单位（如每币）的风险
        if risk_per_unit == 0:
            return 0
        position_size = risk_amount / risk_per_unit
        return position_size

    def place_reduce_only_stop_loss(self, symbol, size, stop_price):
        """
        下只减仓的止损订单，并确保拿到交易所的订单号
        """
        max_attempts = 5
        attempt = 0
        
        while attempt < max_attempts:
            try:
                attempt += 1
                self.logger.info("尝试下只减仓止损订单，第%d次尝试" % attempt)
                
                # 下只减仓的止损订单
                sl_order = self.trader.place_order(
                    symbol=symbol,
                    side='SELL',
                    order_type='STOP_MARKET',
                    quantity=size,
                    stop_price=stop_price,
                    reduce_only=True
                )
                
                if sl_order and 'orderId' in sl_order:
                    self.logger.info("只减仓止损订单成功: 订单ID %s" % sl_order['orderId'])
                    return sl_order['orderId']
                else:
                    self.logger.error("只减仓止损订单失败，响应: %s" % sl_order)
                    if attempt < max_attempts:
                        time.sleep(1)  # 等待1秒后重试
            except Exception as e:
                self.logger.error("下只减仓止损订单时发生异常: %s" % str(e))
                if attempt < max_attempts:
                    time.sleep(1)  # 等待1秒后重试
        
        self.logger.error("经过%d次尝试后，只减仓止损订单仍然失败" % max_attempts)
        return None

    def update_trailing_stop(self, symbol, current_price):
        """
        更新保本止损订单
        当价格上涨超过开仓价2%时，将保本止损价移动到开仓价+0.3%的位置
        """
        if not self.has_open_position or not self.position_info:
            return

        entry_price = self.position_info['entry_price']
        current_stop_loss = self.position_info['stop_loss']
        
        # 检查是否触发保本止损条件
        price_increase = (current_price - entry_price) / entry_price
        
        if price_increase >= self.trailing_stop_threshold:
            # 计算新的保本止损价格
            new_stop_loss = entry_price * (1 + self.trailing_stop_offset)
            
            # 只有当新的止损价高于当前止损价时才更新
            if new_stop_loss > current_stop_loss:
                self.logger.info("触发保本止损更新: 当前价格=%.4f, 开仓价=%.4f, 新止损价=%.4f" % (current_price, entry_price, new_stop_loss))
                
                # 取消当前止损订单
                if 'stop_loss' in self.open_orders:
                    cancel_result = self.trader.cancel_order(symbol, self.open_orders['stop_loss'])
                    if cancel_result:
                        self.logger.info("已取消原止损订单: %s" % self.open_orders['stop_loss'])
                    else:
                        self.logger.error("取消原止损订单失败: %s" % self.open_orders['stop_loss'])
                
                # 下新的保本止损订单
                new_sl_order_id = self.place_reduce_only_stop_loss(symbol, self.position_info['size'], new_stop_loss)
                if new_sl_order_id:
                    self.open_orders['stop_loss'] = new_sl_order_id
                    self.position_info['stop_loss'] = new_stop_loss
                    self.logger.info("保本止损订单更新成功: 订单ID %s, 止损价=%.4f" % (new_sl_order_id, new_stop_loss))
                else:
                    self.logger.error("保本止损订单更新失败")

    def execute_trade(self, symbol, signal_info, account_balance):
        """
        执行交易：完整流程，包含下单、止损止盈设置
        """
        if signal_info['signal'] == 'BUY':
            size = self.calculate_position_size(account_balance, signal_info['entry_price'], signal_info['stop_loss'])
            if size > 0:
                self.logger.info(" >>> 开仓: 数量 %.6f | 入场 %.4f" % (size, signal_info['entry_price']))
                
                # 设置逐仓模式和10倍杠杆
                try:
                    # 先设置逐仓模式
                    margin_result = self.trader.http.set_margin_type(symbol, 'ISOLATED')
                    if margin_result and 'success' in margin_result:
                        self.logger.info("成功设置%s为逐仓模式" % symbol)
                    else:
                        self.logger.error("设置逐仓模式失败: %s" % margin_result)
                
                    # 再设置10倍杠杆
                    leverage_result = self.trader.http.set_leverage(symbol, 10)
                    if leverage_result and ('leverage' in leverage_result or 'success' in leverage_result):
                        self.logger.info("成功设置%s杠杆为10倍" % symbol)
                    else:
                        self.logger.error("设置杠杆失败: %s" % leverage_result)
                except Exception as e:
                    self.logger.error("设置保证金模式或杠杆时发生错误: %s" % str(e))
                
                # 下市价单开仓
                order = self.trader.place_order(
                    symbol=symbol,
                    side='BUY',
                    order_type='MARKET',
                    quantity=size
                )
                
                if order and 'orderId' in order:
                    self.logger.info("开仓订单成功: 订单ID %s" % order['orderId'])
                    self.has_open_position = True
                    self.position_info = {
                        'symbol': symbol,
                        'size': size,
                        'entry_price': signal_info['entry_price'],
                        'stop_loss': signal_info['stop_loss'],
                        'take_profit': signal_info['take_profit']
                    }
                    
                    # 添加到活跃仓位列表（用于多币种支持）
                    self.active_positions[symbol] = self.position_info
                    
                    # 立即下只减仓的止损订单，确保拿到订单号
                    sl_order_id = self.place_reduce_only_stop_loss(symbol, size, signal_info['stop_loss'])
                    if sl_order_id:
                        self.open_orders['stop_loss'] = sl_order_id
                        self.logger.info("基础止损订单设置成功: 订单ID %s" % sl_order_id)
                    else:
                        self.logger.error("基础止损订单设置失败，存在爆仓风险！")
                    
                    # 设置止盈单
                    tp_order = self.trader.place_order(
                        symbol=symbol,
                        side='SELL',
                        order_type='TAKE_PROFIT_MARKET',
                        quantity=size,
                        stop_price=signal_info['take_profit'],
                        reduce_only=True
                    )
                    
                    if tp_order and 'orderId' in tp_order:
                        self.open_orders['take_profit'] = tp_order['orderId']
                        self.logger.info("止盈单设置成功: 订单ID %s" % tp_order['orderId'])
                    else:
                        self.logger.error("止盈单设置失败: %s" % tp_order)
                else:
                    self.logger.error("开仓订单失败")

        elif signal_info['signal'] == 'SELL':
            self.logger.info(" <<< 平仓: 市价卖出")
            
            # 取消所有挂单
            for order_type, order_id in self.open_orders.items():
                cancel_result = self.trader.cancel_order(symbol, order_id)
                if cancel_result:
                    self.logger.info("已取消%s订单: %s" % (order_type, order_id))
            
            # 市价平仓
            if self.position_info and 'size' in self.position_info:
                close_order = self.trader.place_order(
                    symbol=symbol,
                    side='SELL',
                    order_type='MARKET',
                    quantity=self.position_info['size']
                )
                
                if close_order and 'orderId' in close_order:
                    self.logger.info("平仓订单成功: 订单ID %s" % close_order['orderId'])
                else:
                    self.logger.error("平仓订单失败")
            
            # 从活跃仓位列表中移除
            if symbol in self.active_positions:
                del self.active_positions[symbol]
            
            # 重置状态
            self.has_open_position = False
            self.position_info = {}
            self.open_orders = {}

    def get_cached_data(self, cache_key, cache_duration=60):
        """
        🚀 智能缓存机制：获取缓存数据，减少API调用
        """
        current_time = time.time()

        # 检查缓存是否存在且未过期
        if (cache_key in self.price_cache and
            cache_key in self.cache_expiry and
            current_time < self.cache_expiry[cache_key]):
            return self.price_cache[cache_key]

        return None

    def set_cached_data(self, cache_key, data, cache_duration=60):
        """
        💾 设置缓存数据
        """
        current_time = time.time()
        self.price_cache[cache_key] = data
        self.cache_expiry[cache_key] = current_time + cache_duration

    def fetch_klines_to_df(self, symbol, interval='5m', limit=100, use_cache=True):
        """
        📈 优化版K线数据获取：支持智能缓存
        """
        cache_key = f"{symbol}_{interval}_{limit}"

        # 尝试从缓存获取
        if use_cache:
            cached_df = self.get_cached_data(cache_key, cache_duration=30)  # 30秒缓存
            if cached_df is not None:
                self.logger.debug("使用缓存的K线数据: %s" % symbol)
                return cached_df

        try:
            klines = self.trader.get_klines(symbol, interval, limit)
            if not klines:
                self.logger.error("无法获取 %s 的K线数据" % symbol)
                return None

            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])

            # 转换数据类型
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            df.dropna(inplace=True)

            # 缓存数据
            if use_cache:
                self.set_cached_data(cache_key, df, cache_duration=30)
                self.logger.debug("已缓存K线数据: %s" % symbol)

            return df
        except Exception as e:
            self.logger.error("获取 %s K线数据时出错: %s" % (symbol, e))
            return None

    def get_top_symbols_by_change(self):
        """
        获取币安永续合约24小时涨幅最高的前N个币种
        """
        try:
            self.logger.info("开始获取24小时涨幅最高的币种...")

            # 获取所有交易对的24小时统计信息
            tickers = self.trader.http.get('/fapi/v1/ticker/24hr')

            if not tickers or 'error' in tickers:
                self.logger.error("获取市场数据失败: %s" % tickers)
                return []

            # 获取交易所信息，用于过滤活跃币种
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            active_symbols = set()

            if exchange_info and 'symbols' in exchange_info:
                for symbol_info in exchange_info['symbols']:
                    # 只选择状态为TRADING的USDT永续合约
                    if (symbol_info.get('status') == 'TRADING' and
                        symbol_info.get('contractType') == 'PERPETUAL' and
                        symbol_info.get('symbol', '').endswith('USDT')):
                        active_symbols.add(symbol_info['symbol'])

                self.logger.info("发现 %d 个活跃的USDT永续合约" % len(active_symbols))
            else:
                self.logger.warning("无法获取交易所信息，使用所有USDT币对")
                # 如果无法获取交易所信息，回退到原来的逻辑
                active_symbols = None

            # 筛选出USDT交易对并计算涨幅
            usdt_tickers = []
            for ticker in tickers:
                # 只处理USDT交易对
                if isinstance(ticker, dict) and 'symbol' in ticker and ticker['symbol'].endswith('USDT'):
                    symbol = ticker['symbol']

                    # 如果有活跃币种列表，则只处理活跃币种
                    if active_symbols is not None and symbol not in active_symbols:
                        continue

                    try:
                        # 计算24小时涨跌幅
                        price_change_percent = float(ticker.get('priceChangePercent', 0))
                        usdt_tickers.append({
                            'symbol': symbol,
                            'price_change_percent': price_change_percent,
                            'last_price': float(ticker.get('lastPrice', 0)),
                            'volume': float(ticker.get('quoteVolume', 0))
                        })
                    except (ValueError, KeyError, TypeError) as e:
                        # 忽略无法解析的数据
                        continue

            # 按涨幅排序，取前N个
            usdt_tickers.sort(key=lambda x: x['price_change_percent'], reverse=True)
            top_tickers = usdt_tickers[:self.top_symbols_count]

            # 记录日志
            self.logger.info("当前24小时涨幅前%d的币种:" % self.top_symbols_count)
            for i, ticker in enumerate(top_tickers, 1):
                self.logger.info("%d. %s: %.2f%% (价格: %.4f, 成交量: %.2f)" %
                               (i, ticker['symbol'], ticker['price_change_percent'],
                                ticker['last_price'], ticker['volume']))

            # 返回币种列表
            top_symbols = [ticker['symbol'] for ticker in top_tickers]
            self.top_symbols = top_symbols
            return top_symbols

        except Exception as e:
            self.logger.error("获取涨幅最高币种时发生异常: %s" % str(e))
            return []

    def update_top_symbols_continuously(self, interval=300):
        """
        持续更新涨幅最高的币种列表
        Args:
            interval: 更新间隔（秒），默认300秒（5分钟）
        """
        self.logger.info("开始持续更新涨幅最高币种列表，间隔%d秒" % interval)
        
        try:
            while True:
                try:
                    # 获取最新的前N个币种
                    top_symbols = self.get_top_symbols_by_change()
                    if top_symbols:
                        self.top_symbols = top_symbols
                        self.logger.info("已更新追踪币种列表: %s" % ', '.join(top_symbols))
                    else:
                        self.logger.warning("未能获取到有效的币种列表")
                    
                    # 等待下次更新
                    self.logger.info("等待%d秒后更新币种列表..." % interval)
                    time.sleep(interval)
                except Exception as e:
                    self.logger.error("更新币种列表时发生错误: %s" % str(e))
                    # 出现错误时等待一段时间再继续
                    time.sleep(60)  # 等待1分钟再重试
        except KeyboardInterrupt:
            self.logger.info("币种列表更新被用户手动停止")
        except Exception as e:
            self.logger.error("币种列表更新中发生致命错误: %s" % str(e))

    def run_strategy_optimized(self, symbol):
        """
        🎯 优化版策略运行：分层频率控制 + 完整策略逻辑
        """
        # 第一层：超高频风险监控（生命线）
        if self.should_execute_module('emergency_check'):
            self.emergency_risk_check(symbol)

        if self.should_execute_module('risk_monitor'):
            self.monitor_position_risk(symbol)

        if self.should_execute_module('trailing_stop_update'):
            self.update_trailing_stop_optimized(symbol)

        # 第二层：高频信号检测（机会捕捉）
        if self.should_execute_module('price_monitor'):
            self.monitor_price_action(symbol)

        if self.should_execute_module('trend_check'):
            self.quick_trend_check(symbol)

        # 第三层：中频策略执行（核心逻辑）
        if self.should_execute_module('signal_generation'):
            self.full_signal_analysis(symbol)

        if self.should_execute_module('position_management'):
            self.manage_positions(symbol)

        # 第四层：低频市场扫描（全局视野）
        if self.should_execute_module('account_check'):
            self.check_account_status()

    def full_signal_analysis(self, symbol):
        """
        🎯 完整信号分析：包含原有的策略核心逻辑
        """
        self.logger.info("开始运行三态狙击手策略 for %s" % symbol)

        # 🌐 显示网络状态
        network_status = self.get_network_status()
        if "error" not in network_status:
            self.logger.info("🌐 网络状态: %s | 质量: %d/100" %
                           (network_status.get("connection_mode", "未知"),
                            network_status.get("quality_score", 0)))
            if network_status.get("response_time"):
                self.logger.info("⚡ 平均响应时间: %.2fms" % network_status["response_time"])

        # 获取账户余额
        account_balance = self.trader.get_total_balance()
        self.logger.info("账户余额: %.8f USDT" % account_balance)

        # 获取K线数据
        df = self.fetch_klines_to_df(symbol)
        if df is None or len(df) < 50:
            self.logger.error("K线数据不足，无法进行分析")
            return

        # 生成信号
        signal_info = self.generate_signal(df)

        # 执行交易
        if signal_info['signal']:
            self.execute_trade(symbol, signal_info, account_balance)
        elif self.has_open_position and self.position_info.get('symbol') == symbol:
            # 如果有持仓，检查是否需要更新保本止损
            current_price = df['close'].iloc[-1]
            self.update_trailing_stop(symbol, current_price)

        self.logger.info("当前市场状态: %s" % signal_info['current_state'])

    def emergency_risk_check(self, symbol):
        """
        🚨 紧急风险检查：1秒级监控
        """
        if not self.has_open_position:
            return

        try:
            # 获取最新价格（使用ticker，更快）
            ticker = self.trader.http.get(f'/fapi/v1/ticker/price?symbol={symbol}')
            if ticker and 'price' in ticker:
                current_price = float(ticker['price'])
                entry_price = self.position_info.get('entry_price', 0)

                # 检查是否出现极端亏损（超过5%）
                if entry_price > 0:
                    loss_pct = (entry_price - current_price) / entry_price
                    if loss_pct > 0.05:  # 亏损超过5%
                        self.logger.warning("⚠️ 检测到极端亏损%.2f%%，触发紧急检查" % (loss_pct * 100))
                        # 可以在这里添加紧急平仓逻辑
        except Exception as e:
            self.logger.error("紧急风险检查失败: %s" % str(e))

    def monitor_position_risk(self, symbol):
        """
        📊 仓位风险监控：2秒级检查
        """
        if not self.has_open_position:
            return

        try:
            # 获取当前价格
            current_price = self.get_current_price(symbol)
            if current_price:
                self.update_trailing_stop(symbol, current_price)
        except Exception as e:
            self.logger.error("仓位风险监控失败: %s" % str(e))

    def run_strategy(self, symbol):
        """
        运行策略的核心方法（保持向后兼容）
        """
        self.logger.info("开始运行三态狙击手策略 for %s" % symbol)

        # 🌐 显示网络状态
        network_status = self.get_network_status()
        if "error" not in network_status:
            self.logger.info("🌐 网络状态: %s | 质量: %d/100" %
                           (network_status.get("connection_mode", "未知"),
                            network_status.get("quality_score", 0)))
            if network_status.get("response_time"):
                self.logger.info("⚡ 平均响应时间: %.2fms" % network_status["response_time"])

        # 获取账户余额
        account_balance = self.trader.get_total_balance()
        self.logger.info("账户余额: %.8f USDT" % account_balance)

        # 获取K线数据
        df = self.fetch_klines_to_df(symbol)
        if df is None or len(df) < 50:
            self.logger.error("K线数据不足，无法进行分析")
            return

        # 生成信号
        signal_info = self.generate_signal(df)

        # 执行交易
        if signal_info['signal']:
            self.execute_trade(symbol, signal_info, account_balance)
        elif self.has_open_position and self.position_info.get('symbol') == symbol:
            # 如果有持仓，检查是否需要更新保本止损
            current_price = df['close'].iloc[-1]
            self.update_trailing_stop(symbol, current_price)

        self.logger.info("当前市场状态: %s" % signal_info['current_state'])

    def get_current_price(self, symbol):
        """
        🚀 快速获取当前价格（优先使用缓存）
        """
        cache_key = f"price_{symbol}"
        cached_price = self.get_cached_data(cache_key, cache_duration=5)  # 5秒缓存

        if cached_price is not None:
            return cached_price

        try:
            ticker = self.trader.http.get(f'/fapi/v1/ticker/price?symbol={symbol}')
            if ticker and 'price' in ticker:
                price = float(ticker['price'])
                self.set_cached_data(cache_key, price, cache_duration=5)
                return price
        except Exception as e:
            self.logger.error("获取价格失败: %s" % str(e))

        return None

    def update_trailing_stop_optimized(self, symbol):
        """
        ⚡ 优化版保本止损更新：3秒级检查
        """
        if not self.has_open_position or symbol != self.position_info.get('symbol'):
            return

        current_price = self.get_current_price(symbol)
        if current_price:
            self.update_trailing_stop(symbol, current_price)

    def monitor_price_action(self, symbol):
        """
        📈 价格行为监控：30秒级检查
        """
        try:
            # 获取简化的价格数据
            current_price = self.get_current_price(symbol)
            if not current_price:
                return

            # 检查价格突破（简化版）
            cache_key = f"price_history_{symbol}"
            price_history = self.get_cached_data(cache_key, cache_duration=300) or []

            # 添加当前价格到历史
            price_history.append({
                'price': current_price,
                'timestamp': time.time()
            })

            # 保持最近10个价格点
            if len(price_history) > 10:
                price_history = price_history[-10:]

            self.set_cached_data(cache_key, price_history, cache_duration=300)

            # 检查价格突破
            if len(price_history) >= 5:
                recent_high = max([p['price'] for p in price_history[-5:]])
                recent_low = min([p['price'] for p in price_history[-5:]])

                # 简单的突破检测
                if current_price > recent_high * 1.002:  # 突破上方0.2%
                    self.logger.info("🔥 检测到向上突破: %s @ %.4f" % (symbol, current_price))
                elif current_price < recent_low * 0.998:  # 突破下方0.2%
                    self.logger.info("📉 检测到向下突破: %s @ %.4f" % (symbol, current_price))

        except Exception as e:
            self.logger.error("价格行为监控失败: %s" % str(e))

    def quick_trend_check(self, symbol):
        """
        🎯 快速趋势检查：45秒级确认
        """
        try:
            # 获取简化的K线数据（只要最近几根）
            df = self.fetch_klines_to_df(symbol, limit=20, use_cache=True)
            if df is None or len(df) < 10:
                return

            # 快速趋势判断
            recent_closes = df['close'].tail(5)
            ma5 = recent_closes.mean()
            current_price = recent_closes.iloc[-1]

            trend = "上升" if current_price > ma5 else "下降"
            self.logger.debug("快速趋势检查 %s: %s (价格: %.4f, MA5: %.4f)" %
                            (symbol, trend, current_price, ma5))

        except Exception as e:
            self.logger.error("快速趋势检查失败: %s" % str(e))

    def full_signal_analysis(self, symbol):
        """
        🔍 完整信号分析：2分钟级深度分析
        """
        try:
            # 获取完整K线数据
            df = self.fetch_klines_to_df(symbol, use_cache=True)
            if df is None or len(df) < 50:
                return

            # 生成完整信号
            signal_info = self.generate_signal(df)

            # 执行交易决策
            if signal_info['signal']:
                account_balance = self.trader.get_total_balance()
                self.execute_trade(symbol, signal_info, account_balance)

        except Exception as e:
            self.logger.error("完整信号分析失败: %s" % str(e))

    def manage_positions(self, symbol):
        """
        💼 仓位管理：3分钟级检查
        """
        try:
            if not self.has_open_position:
                return

            # 检查仓位状态
            position_info = self.position_info
            if position_info.get('symbol') != symbol:
                return

            current_price = self.get_current_price(symbol)
            if not current_price:
                return

            entry_price = position_info.get('entry_price', 0)
            if entry_price > 0:
                pnl_pct = (current_price - entry_price) / entry_price * 100
                self.logger.info("仓位管理检查 %s: 盈亏 %.2f%%" % (symbol, pnl_pct))

        except Exception as e:
            self.logger.error("仓位管理失败: %s" % str(e))

    def check_account_status(self):
        """
        💰 账户状态检查：30分钟级检查
        """
        try:
            account_balance = self.trader.get_total_balance()
            self.logger.info("账户余额检查: %.8f USDT" % account_balance)

            current_time = time.time()

            # 🚀 智能代理优化检查
            if current_time - self.last_proxy_optimization >= self.proxy_optimization_interval:
                self.optimize_proxy_connection()
                self.last_proxy_optimization = current_time



            # 可以添加更多账户相关检查
            # 如：保证金率、未实现盈亏等

        except Exception as e:
            self.logger.error("账户状态检查失败: %s" % str(e))



    def optimize_proxy_connection(self):
        """
        🚀 智能代理连接优化
        """
        try:
            self.logger.info("🚀 开始智能代理连接优化...")

            # 获取当前代理状态
            proxy_status = self.proxy_manager.get_proxy_status()
            self.logger.info("当前代理状态: 启用=%s, 工作=%s" % (proxy_status['enabled'], proxy_status['working']))

            # 执行自动优化
            optimization_result = self.proxy_manager.auto_optimize_connection()

            if optimization_result.get("optimized_setting") != optimization_result.get("original_setting"):
                self.logger.info("🔄 代理设置已优化: %s" % optimization_result.get("recommendation", ""))
                if optimization_result.get("performance_gain"):
                    self.logger.info("📈 性能提升: %s" % optimization_result["performance_gain"])
            else:
                self.logger.info("✅ 当前代理设置已是最优")

            # 监控连接质量
            quality_report = self.proxy_manager.monitor_connection_quality()
            self.logger.info("📊 连接质量分数: %d/100" % quality_report.get("quality_score", 0))

            for recommendation in quality_report.get("recommendations", []):
                self.logger.info("💡 建议: %s" % recommendation)

        except Exception as e:
            self.logger.error("❌ 代理连接优化失败: %s" % str(e))

    def get_network_status(self):
        """
        🌐 获取网络状态信息
        """
        try:
            # 获取代理状态
            proxy_status = self.proxy_manager.get_proxy_status()

            # 获取连接质量
            quality_report = self.proxy_manager.monitor_connection_quality()

            network_info = {
                "proxy_enabled": proxy_status.get("enabled", False),
                "proxy_working": proxy_status.get("working", False),
                "connection_mode": "代理" if proxy_status.get("enabled") else "直连",
                "quality_score": quality_report.get("quality_score", 0),
                "last_test": proxy_status.get("last_test"),
                "response_time": proxy_status.get("average_response_time")
            }

            return network_info

        except Exception as e:
            self.logger.error("获取网络状态失败: %s" % str(e))
            return {"error": str(e)}

    def run_continuously_optimized(self, symbol, use_optimized=True):
        """
        🚀 优化版持续运行：智能分层频率控制
        """
        self.logger.info("🎯 开始运行优化版三态狙击手策略 for %s" % symbol)
        self.logger.info("📊 分层频率配置:")
        for module, freq in self.frequency_config.items():
            self.logger.info("  - %s: %d秒" % (module, freq))

        try:
            while True:
                try:
                    if use_optimized:
                        # 使用优化的分层频率控制
                        self.run_strategy_optimized(symbol)
                    else:
                        # 使用传统方式
                        self.run_strategy(symbol)

                    # 最小休眠间隔（避免过度占用CPU）
                    time.sleep(0.5)

                except Exception as e:
                    self.logger.error("运行策略时发生错误: %s" % str(e))
                    time.sleep(5)  # 错误时等待5秒
        except KeyboardInterrupt:
            self.logger.info("策略被用户手动停止")
        except Exception as e:
            self.logger.error("策略运行中发生致命错误: %s" % str(e))

    def run_continuously(self, symbol, interval=300):
        """
        持续运行策略（保持向后兼容）
        Args:
            symbol: 交易对
            interval: 运行间隔（秒），默认300秒（5分钟）
        """
        self.logger.info("开始持续运行三态狙击手策略 for %s，间隔%d秒" % (symbol, interval))

        try:
            while True:
                try:
                    # 运行策略
                    self.run_strategy(symbol)

                    # 等待下次运行
                    self.logger.info("等待%d秒后进行下次检查..." % interval)
                    time.sleep(interval)
                except Exception as e:
                    self.logger.error("运行策略时发生错误: %s" % str(e))
                    # 出现错误时等待一段时间再继续
                    time.sleep(60)  # 等待1分钟再重试
        except KeyboardInterrupt:
            self.logger.info("策略被用户手动停止")
        except Exception as e:
            self.logger.error("策略运行中发生致命错误: %s" % str(e))

    def run_multi_symbol_strategy(self, interval=300):
        """
        运行多币种策略
        Args:
            interval: 每个币种检查间隔（秒）
        """
        self.logger.info("开始运行多币种三态狙击手策略")
        
        try:
            while True:
                try:
                    # 获取当前追踪的币种列表
                    if not self.top_symbols:
                        self.logger.info("正在获取初始币种列表...")
                        self.top_symbols = self.get_top_symbols_by_change()
                        if not self.top_symbols:
                            self.logger.error("无法获取币种列表，等待后重试...")
                            time.sleep(60)
                            continue
                    
                    self.logger.info("当前追踪的币种: %s" % ', '.join(self.top_symbols))
                    
                    # 检查每个币种的交易机会
                    for symbol in self.top_symbols:
                        try:
                            # 检查是否已达到最大仓位数
                            if len(self.active_positions) >= self.max_positions:
                                self.logger.info("已达到最大仓位数(%d)，跳过其他币种检查" % self.max_positions)
                                break

                            # 检查该币种是否已有仓位
                            if symbol in self.active_positions:
                                self.logger.info("币种%s已有仓位，跳过" % symbol)
                                continue

                            self.logger.info("检查币种: %s" % symbol)

                            # 运行单个币种的策略
                            self.run_strategy(symbol)

                            # 短暂延迟避免API限制
                            time.sleep(1)

                        except Exception as e:
                            self.logger.error("检查币种%s时发生错误: %s" % (symbol, str(e)))
                            continue
                    
                    # 等待下次运行
                    self.logger.info("等待%d秒后进行下次检查..." % interval)
                    time.sleep(interval)
                    
                except Exception as e:
                    self.logger.error("运行多币种策略时发生错误: %s" % str(e))
                    # 出现错误时等待一段时间再继续
                    time.sleep(60)  # 等待1分钟再重试
                    
        except KeyboardInterrupt:
            self.logger.info("多币种策略被用户手动停止")
        except Exception as e:
            self.logger.error("多币种策略运行中发生致命错误: %s" % str(e))

    def run_multi_symbol_strategy_optimized(self):
        """
        🚀 优化版多币种策略：智能频率控制
        """
        self.logger.info("🎯 开始运行优化版多币种三态狙击手策略")

        try:
            while True:
                try:
                    # 第四层：低频市场扫描
                    if self.should_execute_module('symbol_ranking'):
                        if not self.top_symbols:
                            self.logger.info("正在获取初始币种列表...")
                            self.top_symbols = self.get_top_symbols_by_change()
                        else:
                            # 定期更新币种列表
                            new_symbols = self.get_top_symbols_by_change()
                            if new_symbols:
                                self.top_symbols = new_symbols

                    if not self.top_symbols:
                        self.logger.error("无法获取币种列表，等待后重试...")
                        time.sleep(60)
                        continue

                    # 检查每个币种
                    symbols_to_remove = []  # 记录需要移除的无效币种

                    for symbol in self.top_symbols:
                        try:
                            # 检查是否已达到最大仓位数
                            if len(self.active_positions) >= self.max_positions:
                                break

                            # 检查该币种是否已有仓位
                            if symbol in self.active_positions:
                                # 对已有仓位的币种进行风险监控和策略分析
                                self.run_strategy(symbol)
                            else:
                                # 对新币种进行完整策略分析
                                self.run_strategy(symbol)

                            # 短暂延迟避免API限制
                            time.sleep(0.1)

                        except Exception as e:
                            error_msg = str(e).lower()
                            if "invalid symbol" in error_msg or "symbol" in error_msg:
                                self.logger.warning("⚠️ 币种 %s 无效，将从列表中移除: %s" % (symbol, str(e)))
                                symbols_to_remove.append(symbol)
                            else:
                                self.logger.error("检查币种%s时发生错误: %s" % (symbol, str(e)))
                            continue

                    # 移除无效币种
                    for symbol in symbols_to_remove:
                        if symbol in self.top_symbols:
                            self.top_symbols.remove(symbol)
                            self.logger.info("🗑️ 已移除无效币种: %s" % symbol)

                    # 最小休眠间隔
                    time.sleep(0.5)

                except Exception as e:
                    self.logger.error("运行优化版多币种策略时发生错误: %s" % str(e))
                    time.sleep(5)

        except KeyboardInterrupt:
            self.logger.info("优化版多币种策略被用户手动停止")
        except Exception as e:
            self.logger.error("优化版多币种策略运行中发生致命错误: %s" % str(e))

def main():
    """
    主函数，用于独立运行策略
    """
    # 🚀 智能代理配置
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {
                # 🎯 智能代理设置
                'smart_proxy': True,              # 启用智能代理
                'auto_detect': True,              # 自动检测最佳连接方式
                'target_server_ip': '*************',  # 目标服务器IP

                # 传统代理设置（作为备选）
                'enabled': False,                 # 初始禁用，由智能检测决定
                'type': 'http',
                'host': '127.0.0.1',
                'port': 7897,
                'username': '',
                'password': ''
            }
        }
    }
    
    # 创建策略实例
    strategy = ThreeStateSniper(config)

    # 🚀 显示智能代理状态
    print("\n🌐 智能代理状态:")
    network_status = strategy.get_network_status()
    if "error" not in network_status:
        print(f"  📡 连接模式: {network_status.get('connection_mode', '未知')}")
        print(f"  📊 连接质量: {network_status.get('quality_score', 0)}/100")
        if network_status.get('response_time'):
            print(f"  ⚡ 响应时间: {network_status['response_time']:.2f}ms")
    else:
        print(f"  ❌ 网络状态检查失败: {network_status['error']}")

    # 询问用户运行模式
    print("\n🎯 请选择运行模式:")
    print("1. 单币种模式（传统）")
    print("2. 多币种模式")
    print("3. 指定币种模式")
    print("4. 🚀 单币种优化模式（推荐）")
    print("5. 🚀 多币种优化模式（推荐）")
    print("6. 🌐 网络连接测试模式")

    try:
        choice = input("请输入选择 (1-6): ").strip()
    except KeyboardInterrupt:
        print("\n用户取消输入")
        return
    
    if choice == "2":
        # 多币种模式
        print("启动多币种模式...")
        strategy.run_multi_symbol_strategy(interval=300)
    elif choice == "3":
        # 指定币种模式
        symbol = input("请输入交易对 (如: BTCUSDT, ethusdt): ").strip()
        if symbol:
            symbol = symbol.upper()
            print(f"启动指定币种模式，交易对: {symbol}")
            strategy.run_continuously(symbol, interval=300)
        else:
            print("未输入有效的交易对，使用默认 AVNTUSDT")
            strategy.run_continuously('AVNTUSDT', interval=300)
    elif choice == "4":
        # 🚀 单币种优化模式
        symbol = input("请输入交易对 (默认 ETHUSDT): ").strip()
        if not symbol:
            symbol = 'ETHUSDT'
        symbol = symbol.upper()
        print(f"🚀 启动单币种优化模式，交易对: {symbol}")
        print("📊 使用智能分层频率控制，性能大幅提升！")
        strategy.run_continuously_optimized(symbol, use_optimized=True)
    elif choice == "5":
        # 🚀 多币种优化模式
        print("🚀 启动多币种优化模式...")
        print("📊 使用智能分层频率控制，性能大幅提升！")
        strategy.run_multi_symbol_strategy_optimized()
    elif choice == "6":
        # 🌐 网络连接测试模式
        print("🌐 开始网络连接测试...")
        test_network_connection(strategy)
    else:
        # 单币种模式 (默认)
        symbol = input("请输入交易对 (默认 ETHUSDT): ").strip()
        if not symbol:
            symbol = 'ETHUSDT'
        symbol = symbol.upper()
        print(f"启动单币种模式，交易对: {symbol}")
        strategy.run_continuously(symbol, interval=300)

def test_network_connection(strategy):
    """
    🌐 网络连接测试功能
    """
    print("\n🔧 开始全面网络连接测试...")

    try:
        # 1. 显示当前环境信息
        print("\n📍 当前环境信息:")
        proxy_manager = strategy.proxy_manager
        env_info = proxy_manager.get_environment_info()

        environment = env_info.get("environment", {})
        print(f"  🖥️  平台: {environment.get('platform', '未知')}")
        print(f"  🏠 主机名: {environment.get('hostname', '未知')}")
        print(f"  📍 本地IP: {environment.get('local_ip', '未知')}")
        print(f"  🌐 公网IP: {environment.get('public_ip', '未知')}")

        # 2. 执行连接测试
        print("\n🚀 执行智能连接优化...")
        optimization_result = proxy_manager.auto_optimize_connection()

        print(f"  📊 原始设置: {'代理' if optimization_result.get('original_setting') else '直连'}")
        print(f"  🎯 优化设置: {'代理' if optimization_result.get('optimized_setting') else '直连'}")
        print(f"  💡 建议: {optimization_result.get('recommendation', '无')}")

        if optimization_result.get('performance_gain'):
            print(f"  📈 性能提升: {optimization_result['performance_gain']}")

        # 3. 显示详细测试结果
        print("\n📊 详细测试结果:")

        direct_test = optimization_result.get('direct_test', {})
        if direct_test:
            if direct_test.get('success'):
                print(f"  ✅ 直连: 成功 ({direct_test.get('response_time', 0):.2f}ms)")
            else:
                print(f"  ❌ 直连: 失败 - {direct_test.get('error', '未知错误')}")

        proxy_test = optimization_result.get('proxy_test', {})
        if proxy_test:
            if proxy_test.get('success'):
                print(f"  ✅ 代理: 成功 ({proxy_test.get('response_time', 0):.2f}ms)")
            else:
                print(f"  ❌ 代理: 失败 - {proxy_test.get('error', '未知错误')}")

        # 4. 连接质量监控
        print("\n📈 连接质量监控:")
        quality_report = proxy_manager.monitor_connection_quality()
        print(f"  📊 质量分数: {quality_report.get('quality_score', 0)}/100")
        print(f"  🔗 当前模式: {quality_report.get('current_mode', '未知')}")

        for recommendation in quality_report.get('recommendations', []):
            print(f"  💡 {recommendation}")

        # 5. 询问是否应用优化设置
        if optimization_result.get('optimized_setting') != optimization_result.get('original_setting'):
            apply = input("\n🔄 是否应用优化设置? (y/n): ").strip().lower()
            if apply == 'y':
                print("✅ 优化设置已应用！")
            else:
                # 恢复原始设置
                proxy_manager.enabled = optimization_result.get('original_setting', False)
                print("🔄 已恢复原始设置")

        print("\n✅ 网络连接测试完成！")

    except Exception as e:
        print(f"\n❌ 网络连接测试失败: {e}")

    input("\n按回车键返回主菜单...")
    main()  # 返回主菜单

if __name__ == "__main__":
    main()