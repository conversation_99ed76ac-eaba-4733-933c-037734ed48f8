#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能代理功能测试脚本
测试自动IP检测和代理选择功能
"""

import sys
import time
import logging
from proxy_manager import ProxyManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_smart_proxy():
    """测试智能代理功能"""
    print("🚀 智能代理功能测试")
    print("=" * 50)
    
    # 测试配置
    config = {
        'proxy': {
            # 🎯 智能代理设置
            'smart_proxy': True,              # 启用智能代理
            'auto_detect': True,              # 自动检测最佳连接方式
            'target_server_ip': '*************',  # 目标服务器IP
            
            # 传统代理设置
            'enabled': False,                 # 初始禁用，由智能检测决定
            'type': 'http',
            'host': '127.0.0.1',
            'port': 7897,
            'username': '',
            'password': ''
        }
    }
    
    try:
        # 创建代理管理器
        print("\n1️⃣ 创建代理管理器...")
        proxy_manager = ProxyManager(config)
        
        # 显示环境信息
        print("\n2️⃣ 环境信息:")
        env_info = proxy_manager.get_environment_info()
        environment = env_info.get("environment", {})
        
        print(f"  🖥️  平台: {environment.get('platform', '未知')}")
        print(f"  🏠 主机名: {environment.get('hostname', '未知')}")
        print(f"  📍 本地IP: {environment.get('local_ip', '未知')}")
        print(f"  🌐 公网IP: {environment.get('public_ip', '未知')}")
        print(f"  🎯 目标服务器: {proxy_manager.target_server_ip}")
        
        # 显示智能检测结果
        print(f"\n3️⃣ 智能检测结果:")
        print(f"  🔧 代理启用: {proxy_manager.enabled}")
        
        # 执行连接测试
        print("\n4️⃣ 连接测试:")
        test_results = proxy_manager.run_connection_tests()
        
        # 直连测试
        direct_test = test_results.get("direct_connection", {})
        if direct_test.get("success"):
            print(f"  ✅ 直连: 成功 ({direct_test.get('response_time', 0):.2f}ms)")
        else:
            print(f"  ❌ 直连: 失败 - {direct_test.get('error', '未知错误')}")
        
        # 代理测试
        proxy_test = test_results.get("proxy_connection")
        if proxy_test:
            if proxy_test.get("success"):
                print(f"  ✅ 代理: 成功 ({proxy_test.get('response_time', 0):.2f}ms)")
            else:
                print(f"  ❌ 代理: 失败 - {proxy_test.get('error', '未知错误')}")
        else:
            print("  ⏭️  代理: 未测试（代理未启用）")
        
        # 显示建议
        print("\n5️⃣ 系统建议:")
        recommendations = test_results.get("recommendations", [])
        for rec in recommendations:
            print(f"  💡 {rec}")
        
        # 执行自动优化
        print("\n6️⃣ 自动优化:")
        optimization_result = proxy_manager.auto_optimize_connection()
        
        print(f"  📊 原始设置: {'代理' if optimization_result.get('original_setting') else '直连'}")
        print(f"  🎯 优化设置: {'代理' if optimization_result.get('optimized_setting') else '直连'}")
        print(f"  💡 建议: {optimization_result.get('recommendation', '无')}")
        
        if optimization_result.get('performance_gain'):
            print(f"  📈 性能提升: {optimization_result['performance_gain']}")
        
        # 连接质量监控
        print("\n7️⃣ 连接质量监控:")
        quality_report = proxy_manager.monitor_connection_quality()
        print(f"  📊 质量分数: {quality_report.get('quality_score', 0)}/100")
        print(f"  🔗 当前模式: {quality_report.get('current_mode', '未知')}")
        
        for recommendation in quality_report.get('recommendations', []):
            print(f"  💡 {recommendation}")
        
        print("\n✅ 智能代理测试完成！")
        
        # 显示最终状态
        final_status = proxy_manager.get_proxy_status()
        print(f"\n🎯 最终代理状态:")
        print(f"  启用: {final_status.get('enabled', False)}")
        print(f"  工作: {final_status.get('working', False)}")
        if final_status.get('average_response_time'):
            print(f"  平均响应时间: {final_status['average_response_time']:.2f}ms")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_ip_detection():
    """测试IP检测功能"""
    print("\n🔍 IP检测功能测试")
    print("=" * 30)
    
    config = {'proxy': {'target_server_ip': '*************'}}
    proxy_manager = ProxyManager(config)
    
    # 测试不同IP的检测结果
    test_ips = [
        '*************',    # 目标服务器IP
        '*************',    # 同网段IP
        '*************',    # 内网IP
        '*******',          # 谷歌DNS
        '***************',  # 中国DNS
        '*******'           # Cloudflare DNS
    ]
    
    for ip in test_ips:
        should_use_proxy = proxy_manager._should_use_proxy(ip)
        is_china = proxy_manager._is_china_ip(ip)
        print(f"  📍 {ip:15} -> 代理: {'是' if should_use_proxy else '否'} | 中国: {'是' if is_china else '否'}")

if __name__ == "__main__":
    print("🎯 智能代理系统测试")
    print("=" * 60)
    
    # 主要功能测试
    test_smart_proxy()
    
    # IP检测测试
    test_ip_detection()
    
    print("\n🎉 所有测试完成！")
