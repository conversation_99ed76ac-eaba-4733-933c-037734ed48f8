#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 精英交易员全币种策略
覆盖币安期货所有活跃USDT永续合约

核心升级：
1. 全币种扫描（200+币种）
2. 智能分层筛选
3. 动态仓位分配
4. 多策略组合
"""

import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from binance_trader import BinanceTrader
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class EliteFullUniverseStrategy:
    def __init__(self, config):
        """
        初始化全币种精英策略
        """
        self.trader = BinanceTrader(config)
        self.logger = self._setup_logger()
        
        # 🌍 全币种配置
        self.universe_size = 0              # 币种池大小（动态）
        self.scan_batch_size = 50           # 批量扫描大小
        self.max_concurrent = 10            # 最大并发数
        
        # 🎯 策略参数
        self.max_positions = 5              # 增加到5个仓位
        self.risk_per_trade = 0.015         # 降低单笔风险到1.5%
        self.profit_target = 0.05           # 降低目标到5%
        self.stop_loss = 0.025              # 降低止损到2.5%
        
        # 📊 分层筛选参数
        self.tier1_volume_min = 50000000    # 一线币种：5000万USDT
        self.tier2_volume_min = 20000000    # 二线币种：2000万USDT
        self.tier3_volume_min = 5000000     # 三线币种：500万USDT
        
        # 🔄 扫描频率（分层）
        self.tier1_scan_interval = 60      # 一线币种：1分钟
        self.tier2_scan_interval = 180     # 二线币种：3分钟
        self.tier3_scan_interval = 300     # 三线币种：5分钟
        
        # 💰 资金管理
        self.total_balance = 0
        self.available_balance = 0
        self.positions = {}
        self.universe = {}                  # 全币种池
        self.last_universe_update = 0
        
        # 🧵 线程安全
        self.lock = threading.Lock()
        
        self.logger.info("🌍 全币种精英策略初始化完成")
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('EliteFullUniverse')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def update_universe(self):
        """
        🌍 更新全币种池
        获取所有活跃的USDT永续合约
        """
        try:
            self.logger.info("🔄 更新全币种池...")
            
            # 获取交易所信息
            exchange_info = self.trader.http.get('/fapi/v1/exchangeInfo')
            if not exchange_info or 'symbols' not in exchange_info:
                self.logger.error("❌ 无法获取交易所信息")
                return False
            
            # 获取24小时行情
            tickers = self.trader.http.get('/fapi/v1/ticker/24hr')
            if not tickers:
                self.logger.error("❌ 无法获取24小时行情")
                return False
            
            # 创建行情字典
            ticker_dict = {t['symbol']: t for t in tickers}
            
            new_universe = {}
            
            for symbol_info in exchange_info['symbols']:
                symbol = symbol_info.get('symbol', '')
                
                # 只选择活跃的USDT永续合约
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('contractType') == 'PERPETUAL' and
                    symbol.endswith('USDT')):
                    
                    ticker = ticker_dict.get(symbol, {})
                    
                    try:
                        volume = float(ticker.get('quoteVolume', 0))
                        price = float(ticker.get('lastPrice', 0))
                        price_change = float(ticker.get('priceChangePercent', 0))
                        
                        # 基本过滤：价格>0.0001，成交量>100万
                        if price > 0.0001 and volume > 1000000:
                            # 分层分类
                            if volume >= self.tier1_volume_min:
                                tier = 1
                                scan_interval = self.tier1_scan_interval
                            elif volume >= self.tier2_volume_min:
                                tier = 2
                                scan_interval = self.tier2_scan_interval
                            elif volume >= self.tier3_volume_min:
                                tier = 3
                                scan_interval = self.tier3_scan_interval
                            else:
                                continue  # 跳过流动性太差的币种
                            
                            new_universe[symbol] = {
                                'tier': tier,
                                'volume': volume,
                                'price': price,
                                'price_change': price_change,
                                'scan_interval': scan_interval,
                                'last_scan': 0,
                                'last_signal': None
                            }
                            
                    except (ValueError, TypeError):
                        continue
            
            with self.lock:
                self.universe = new_universe
                self.universe_size = len(new_universe)
            
            # 统计各层级币种数量
            tier_counts = {1: 0, 2: 0, 3: 0}
            for data in new_universe.values():
                tier_counts[data['tier']] += 1
            
            self.logger.info(f"✅ 币种池更新完成: 总计{self.universe_size}个币种")
            self.logger.info(f"   一线币种: {tier_counts[1]}个 (扫描间隔:{self.tier1_scan_interval}s)")
            self.logger.info(f"   二线币种: {tier_counts[2]}个 (扫描间隔:{self.tier2_scan_interval}s)")
            self.logger.info(f"   三线币种: {tier_counts[3]}个 (扫描间隔:{self.tier3_scan_interval}s)")
            
            self.last_universe_update = time.time()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 更新币种池失败: {e}")
            return False
    
    def get_symbols_to_scan(self):
        """
        🔍 获取需要扫描的币种列表
        基于分层频率控制
        """
        current_time = time.time()
        symbols_to_scan = []
        
        with self.lock:
            for symbol, data in self.universe.items():
                # 检查是否到了扫描时间
                if current_time - data['last_scan'] >= data['scan_interval']:
                    symbols_to_scan.append(symbol)
        
        return symbols_to_scan
    
    def analyze_symbol_batch(self, symbols):
        """
        📈 批量分析币种
        使用多线程提高效率
        """
        results = []
        
        def analyze_single(symbol):
            try:
                analysis = self.analyze_symbol(symbol)
                if analysis:
                    signal = self.generate_signal(analysis)
                    return {
                        'symbol': symbol,
                        'analysis': analysis,
                        'signal': signal
                    }
            except Exception as e:
                self.logger.debug(f"分析{symbol}失败: {e}")
            return None
        
        # 使用线程池并发分析
        with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
            future_to_symbol = {
                executor.submit(analyze_single, symbol): symbol 
                for symbol in symbols
            }
            
            for future in as_completed(future_to_symbol):
                result = future.result()
                if result:
                    results.append(result)
        
        return results
    
    def analyze_symbol(self, symbol):
        """
        📈 分析单个币种（优化版）
        """
        try:
            # 获取K线数据（使用较短周期提高响应速度）
            klines = self.trader.get_klines(symbol, '5m', 50)
            if not klines or len(klines) < 30:
                return None
            
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # 转换数据类型
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 计算技术指标
            analysis = self._calculate_indicators_fast(df)
            analysis['symbol'] = symbol
            
            # 更新扫描时间
            with self.lock:
                if symbol in self.universe:
                    self.universe[symbol]['last_scan'] = time.time()
            
            return analysis
            
        except Exception as e:
            self.logger.debug(f"分析{symbol}失败: {e}")
            return None
    
    def _calculate_indicators_fast(self, df):
        """
        🧮 快速计算技术指标
        优化版本，减少计算量
        """
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 快速RSI（使用较短周期）
        rsi = self._calculate_rsi_fast(close, 10)
        
        # 简化布林带
        bb_period = 15
        bb_middle = close.rolling(window=bb_period).mean()
        bb_std = close.rolling(window=bb_period).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        # 成交量比率
        volume_ma = volume.rolling(10).mean()
        volume_ratio = volume.iloc[-1] / volume_ma.iloc[-1] if volume_ma.iloc[-1] > 0 else 1
        
        # 价格位置
        current_price = close.iloc[-1]
        if bb_upper.iloc[-1] > bb_lower.iloc[-1]:
            price_position = (current_price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
        else:
            price_position = 0.5
        
        # 简化趋势
        ema_fast = close.ewm(span=8).mean()
        ema_slow = close.ewm(span=16).mean()
        trend_strength = (ema_fast.iloc[-1] - ema_slow.iloc[-1]) / ema_slow.iloc[-1] * 100
        
        return {
            'current_price': current_price,
            'rsi': rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50,
            'bb_position': price_position,
            'volume_ratio': volume_ratio,
            'trend_strength': trend_strength,
            'support_level': bb_lower.iloc[-1],
            'resistance_level': bb_upper.iloc[-1]
        }

    def calculate_position_size_dynamic(self, signal):
        """
        💰 动态仓位计算
        根据币种层级和信心度调整
        """
        if self.available_balance <= 0:
            return 0

        # 基础风险
        base_risk = self.risk_per_trade

        # 根据层级调整风险
        tier_risk_multiplier = {1: 1.2, 2: 1.0, 3: 0.8}
        tier_multiplier = tier_risk_multiplier.get(signal['tier'], 0.8)

        # 根据信心度调整风险
        confidence_multiplier = signal['confidence'] / 100

        # 最终风险
        adjusted_risk = base_risk * tier_multiplier * confidence_multiplier
        adjusted_risk = min(adjusted_risk, 0.025)  # 最大2.5%

        # 计算仓位
        risk_amount = self.available_balance * adjusted_risk
        risk_per_unit = abs(signal['entry_price'] - signal['stop_loss'])

        if risk_per_unit <= 0:
            return 0

        position_size = risk_amount / risk_per_unit

        # 限制最大仓位
        max_position_value = self.available_balance * 0.25  # 单个仓位最大25%
        max_position_size = max_position_value / signal['entry_price']

        return min(position_size, max_position_size)

    def execute_trade_enhanced(self, symbol, signal):
        """
        ⚡ 增强版交易执行
        """
        try:
            if signal['action'] != 'BUY':
                return False

            # 检查仓位限制
            if len(self.positions) >= self.max_positions:
                # 如果是一线币种，可以考虑替换低层级仓位
                if signal['tier'] == 1:
                    self._try_replace_position(symbol, signal)
                return False

            # 计算动态仓位
            position_size = self.calculate_position_size_dynamic(signal)
            if position_size <= 0:
                return False

            self.logger.info(f"🚀 【{signal['tier']}线】买入信号: {symbol}")
            self.logger.info(f"   价格: {signal['entry_price']:.6f}")
            self.logger.info(f"   数量: {position_size:.6f}")
            self.logger.info(f"   止损: {signal['stop_loss']:.6f}")
            self.logger.info(f"   止盈: {signal['take_profit']:.6f}")
            self.logger.info(f"   信心度: {signal['confidence']:.0f}%")
            self.logger.info(f"   信号: {', '.join(signal['signals'])}")

            # 记录持仓
            with self.lock:
                self.positions[symbol] = {
                    'entry_price': signal['entry_price'],
                    'quantity': position_size,
                    'stop_loss': signal['stop_loss'],
                    'take_profit': signal['take_profit'],
                    'entry_time': datetime.now(),
                    'tier': signal['tier'],
                    'signals': signal['signals'],
                    'confidence': signal['confidence']
                }

            return True

        except Exception as e:
            self.logger.error(f"❌ 执行交易失败: {e}")
            return False

    def _try_replace_position(self, new_symbol, new_signal):
        """
        🔄 尝试替换低层级仓位
        """
        # 找到最低层级的仓位
        lowest_tier = 1
        lowest_symbol = None

        with self.lock:
            for symbol, pos in self.positions.items():
                if pos['tier'] > lowest_tier:
                    lowest_tier = pos['tier']
                    lowest_symbol = symbol

        # 如果新信号是一线币种，且现有仓位有三线币种，考虑替换
        if new_signal['tier'] == 1 and lowest_tier == 3:
            self.logger.info(f"🔄 考虑用一线币种{new_symbol}替换三线币种{lowest_symbol}")
            # 这里可以添加更复杂的替换逻辑

    def run_full_universe_strategy(self):
        """
        🌍 运行全币种策略
        """
        self.logger.info("🌍 全币种精英策略启动")

        try:
            # 初始化币种池
            if not self.update_universe():
                self.logger.error("❌ 初始化币种池失败")
                return

            scan_count = 0

            while True:
                start_time = time.time()

                # 定期更新币种池（每小时）
                if time.time() - self.last_universe_update > 3600:
                    self.update_universe()

                # 更新账户余额
                self.update_balance()

                # 管理现有仓位
                self.manage_positions_enhanced()

                # 获取需要扫描的币种
                symbols_to_scan = self.get_symbols_to_scan()

                if symbols_to_scan:
                    scan_count += 1
                    self.logger.info(f"🔍 第{scan_count}轮扫描: {len(symbols_to_scan)}个币种")

                    # 分批扫描
                    for i in range(0, len(symbols_to_scan), self.scan_batch_size):
                        batch = symbols_to_scan[i:i + self.scan_batch_size]

                        # 批量分析
                        results = self.analyze_symbol_batch(batch)

                        # 处理信号
                        buy_signals = [r for r in results if r['signal']['action'] == 'BUY']

                        if buy_signals:
                            # 按信心度和层级排序
                            buy_signals.sort(
                                key=lambda x: (x['signal']['tier'], -x['signal']['confidence'])
                            )

                            self.logger.info(f"📊 发现{len(buy_signals)}个买入信号")

                            # 执行交易
                            for result in buy_signals:
                                if len(self.positions) >= self.max_positions:
                                    break

                                if self.execute_trade_enhanced(
                                    result['symbol'],
                                    result['signal']
                                ):
                                    break  # 成功开仓后暂停，避免过度交易

                        # 批次间延迟
                        time.sleep(1)

                # 计算本轮耗时
                elapsed = time.time() - start_time

                # 动态休眠（确保不会过于频繁）
                min_interval = 30  # 最小30秒间隔
                sleep_time = max(min_interval - elapsed, 5)

                self.logger.info(f"😴 本轮扫描完成，耗时{elapsed:.1f}s，休眠{sleep_time:.1f}s")
                time.sleep(sleep_time)

        except KeyboardInterrupt:
            self.logger.info("🛑 全币种策略被用户停止")
        except Exception as e:
            self.logger.error(f"❌ 策略运行错误: {e}")

    def manage_positions_enhanced(self):
        """
        📊 增强版仓位管理
        """
        if not self.positions:
            return

        self.logger.info(f"📊 管理{len(self.positions)}个仓位")

        for symbol in list(self.positions.keys()):
            try:
                self._manage_single_position(symbol)
            except Exception as e:
                self.logger.error(f"❌ 管理仓位{symbol}失败: {e}")

    def _manage_single_position(self, symbol):
        """管理单个仓位"""
        with self.lock:
            if symbol not in self.positions:
                return
            position = self.positions[symbol].copy()

        # 获取当前价格
        ticker = self.trader.http.get(f'/fapi/v1/ticker/price?symbol={symbol}')
        if not ticker:
            return

        current_price = float(ticker['price'])
        entry_price = position['entry_price']
        pnl_pct = (current_price - entry_price) / entry_price * 100

        # 止损检查
        if current_price <= position['stop_loss']:
            self.logger.info(f"🛑 【{position['tier']}线】{symbol} 触发止损: {pnl_pct:.2f}%")
            self._close_position(symbol, current_price, '止损')
            return

        # 止盈检查
        if current_price >= position['take_profit']:
            self.logger.info(f"🎯 【{position['tier']}线】{symbol} 达到止盈: {pnl_pct:.2f}%")
            self._close_position(symbol, current_price, '止盈')
            return

        # 动态止损（根据层级调整）
        profit_threshold = {1: 2.0, 2: 2.5, 3: 3.0}[position['tier']]

        if pnl_pct > profit_threshold:
            new_stop_loss = entry_price * 1.005  # 移动到盈利0.5%
            if new_stop_loss > position['stop_loss']:
                with self.lock:
                    self.positions[symbol]['stop_loss'] = new_stop_loss
                self.logger.info(f"📈 【{position['tier']}线】{symbol} 移动止损至: {new_stop_loss:.6f}")

        # 时间止损（根据层级调整）
        max_hold_hours = {1: 48, 2: 36, 3: 24}[position['tier']]
        hold_time = datetime.now() - position['entry_time']

        if hold_time > timedelta(hours=max_hold_hours):
            self.logger.info(f"⏰ 【{position['tier']}线】{symbol} 持仓超时，强制平仓: {pnl_pct:.2f}%")
            self._close_position(symbol, current_price, '超时')

    def _close_position(self, symbol, price, reason):
        """平仓"""
        with self.lock:
            if symbol in self.positions:
                position = self.positions[symbol]
                pnl_pct = (price - position['entry_price']) / position['entry_price'] * 100

                self.logger.info(f"📤 平仓 【{position['tier']}线】{symbol}: {reason}, 盈亏: {pnl_pct:.2f}%")
                del self.positions[symbol]

    def update_balance(self):
        """更新账户余额"""
        try:
            self.total_balance = self.trader.get_total_balance()
            self.available_balance = self.total_balance * 0.8

            if len(self.positions) > 0:
                self.logger.info(f"💰 余额: {self.total_balance:.2f} USDT | 仓位: {len(self.positions)}/{self.max_positions}")

        except Exception as e:
            self.logger.error(f"❌ 更新余额失败: {e}")

def main():
    """主函数"""
    config = {
        'api': {
            'api_key': 'nALsAPYzxprXmrgBRx7ZHIr1mnXlDqeZLoH3YL8hu8yu1Gpo65pHlJIDU6IlI20s',
            'api_secret': 'HNBVAQJ4LwlnIuULhCquEG1CQnvYVPo2e0SSkrH1XYMZFdBMEdjvXgrL2QlLEN4u'
        },
        'network': {
            'proxy': {'enabled': False}
        }
    }

    strategy = EliteFullUniverseStrategy(config)
    strategy.run_full_universe_strategy()

if __name__ == "__main__":
    main()
    
    def _calculate_rsi_fast(self, prices, period=10):
        """快速RSI计算"""
        delta = prices.diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        # 避免除零
        rs = gain / loss.replace(0, 0.01)
        return 100 - (100 / (1 + rs))
    
    def generate_signal(self, analysis):
        """
        🎯 生成交易信号（全币种优化版）
        """
        if not analysis:
            return {'action': 'HOLD', 'confidence': 0}
        
        confidence = 0
        signals = []
        
        # 获取币种层级信息
        symbol = analysis['symbol']
        tier = 3  # 默认三线
        with self.lock:
            if symbol in self.universe:
                tier = self.universe[symbol]['tier']
        
        # 根据层级调整信号权重
        tier_multiplier = {1: 1.2, 2: 1.0, 3: 0.8}[tier]
        
        # 信号1：RSI超卖
        if analysis['rsi'] < 25:
            signals.append('RSI极度超卖')
            confidence += 35 * tier_multiplier
        elif analysis['rsi'] < 35:
            signals.append('RSI超卖')
            confidence += 25 * tier_multiplier
        
        # 信号2：布林带位置
        if analysis['bb_position'] < 0.15:
            signals.append('接近布林带下轨')
            confidence += 30 * tier_multiplier
        elif analysis['bb_position'] < 0.3:
            signals.append('布林带下半区')
            confidence += 20 * tier_multiplier
        
        # 信号3：成交量
        if analysis['volume_ratio'] > 3.0:
            signals.append('成交量暴增')
            confidence += 25 * tier_multiplier
        elif analysis['volume_ratio'] > 2.0:
            signals.append('成交量放大')
            confidence += 15 * tier_multiplier
        
        # 信号4：趋势
        if analysis['trend_strength'] > -3:
            signals.append('趋势稳定')
            confidence += 15 * tier_multiplier
        
        # 层级加分
        if tier == 1:
            signals.append('一线币种')
            confidence += 10
        elif tier == 2:
            signals.append('二线币种')
            confidence += 5
        
        # 决策阈值（根据层级调整）
        buy_threshold = {1: 55, 2: 60, 3: 65}[tier]
        watch_threshold = {1: 35, 2: 40, 3: 45}[tier]
        
        if confidence >= buy_threshold:
            action = 'BUY'
        elif confidence >= watch_threshold:
            action = 'WATCH'
        else:
            action = 'HOLD'
        
        return {
            'action': action,
            'confidence': min(confidence, 100),
            'signals': signals,
            'tier': tier,
            'entry_price': analysis['current_price'],
            'stop_loss': analysis['support_level'] * 0.975,
            'take_profit': analysis['current_price'] * 1.05
        }
